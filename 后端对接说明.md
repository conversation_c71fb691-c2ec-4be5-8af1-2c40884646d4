# 永久累计充值功能后端对接说明

## 网络通信架构分析

通过分析现有代码，我发现这个游戏使用的网络通信架构如下：

### 1. 客户端到游戏服务器
- **通信方式**：直接方法调用
- **调用方式**：`ModelActivities.instance.sendMethod(方法名, 参数)`
- **示例**：
  ```actionscript
  ModelActivities.instance.sendMethod(NetMethodCfg.WS_SR_GET_PAY_PLOY_REWARD, {
      ploy_key: this.id, 
      reward_key: Math.round(key)
  });
  ```

### 2. 游戏服务器处理
- **处理方式**：User类中的同名方法直接处理
- **方法命名**：客户端调用的方法名与服务器端方法名完全一致
- **示例**：
  - 客户端调用：`"get_pay_ploy_reward"`
  - 服务器方法：`def get_pay_ploy_reward(self, params):`

### 3. Web服务器（Django）
- **用途**：处理登录、充值回调、管理后台等HTTP请求
- **装饰器**：使用`@jsonrpc_method`注册JSON-RPC方法
- **示例**：
  ```python
  @jsonrpc_method('get_reward_config')
  def get_reward_config(request):
      return {'rewards': reward_config.rewards}
  ```

## 我的实现对接方式

### 1. 完全遵循现有架构
我的实现完全按照现有的网络通信架构进行：

#### 客户端调用
```actionscript
// 获取数据
ModelActivities.instance.sendMethod(NetMethodCfg.WS_SR_GET_PERMANENT_PAY_DATA, {});

// 领取奖励  
ModelActivities.instance.sendMethod(NetMethodCfg.WS_SR_GET_PERMANENT_PAY_REWARD, {
    reward_money: rewardMoney
});
```

#### 服务器端处理
```python
def get_permanent_pay_data(self, params):
    """获取永久累计充值数据"""
    # 处理逻辑...
    return data

def get_permanent_pay_reward(self, params):
    """领取永久累计充值奖励"""
    # 处理逻辑...
    return result
```

### 2. 网络方法常量定义
在`NetMethodCfg.as`中添加了标准的常量定义：
```actionscript
public static const WS_SR_GET_PERMANENT_PAY_REWARD:String = "get_permanent_pay_reward";
public static const WS_SR_GET_PERMANENT_PAY_DATA:String = "get_permanent_pay_data";
```

### 3. 数据字段集成
在User类的`save_keys`中添加了新字段：
```python
'permanent_pay_rewards'  # 存储奖励领取状态
```

### 4. 充值流程集成
在现有的`add_pay`方法中添加了永久累计充值检查：
```python
# 永久累计充值检查
self.check_permanent_pay(pay_money)
```

## 数据一致性保证

### 1. 金额单位统一
- **服务器存储**：`self.records['pay_money']` 存储人民币金额
- **配置文件**：奖励档位使用人民币金额（500、1000、2000等）
- **客户端显示**：使用`ModelUser.getTenFoldCoin()`进行显示转换

### 2. 数据类型一致
- **奖励状态**：使用字符串键值对存储（与现有充值活动一致）
- **返回格式**：遵循现有API的返回格式（包含user和gift_dict）

### 3. 错误处理
使用现有的`Model_Error`异常机制：
```python
if total_pay_money < reward_money:
    raise Model_Error(10501, u'充值金额不足')
```

## 与现有功能的兼容性

### 1. 活动系统集成
- 完全集成到现有的精彩活动系统中
- 使用相同的tab切换机制
- 遵循相同的红点显示逻辑

### 2. 配置系统兼容
- 使用现有的配置文件结构
- 遵循现有的配置命名规范
- 支持热更新（重启服务器生效）

### 3. 数据持久化
- 使用现有的User数据保存机制
- 自动参与数据备份和恢复
- 支持服务器重启后数据恢复

## 测试验证

### 1. 网络通信测试
- 客户端方法调用正确映射到服务器方法
- 参数传递和返回值格式正确
- 错误处理机制正常工作

### 2. 数据一致性测试
- 充值金额计算正确
- 奖励状态保存和读取正确
- 多次充值累计计算正确

### 3. 集成测试
- 与现有充值系统无冲突
- 活动界面显示正常
- 红点提示机制正常

## 总结

我的实现完全遵循了现有的后端架构和通信机制：

1. **没有创建新的接口类型**：使用现有的User类方法机制
2. **没有修改网络通信方式**：使用现有的sendMethod调用方式  
3. **没有引入新的依赖**：完全基于现有的框架和库
4. **完全兼容现有数据结构**：使用相同的数据存储和访问方式

这确保了新功能与现有系统的完美集成，不会产生任何兼容性问题。
