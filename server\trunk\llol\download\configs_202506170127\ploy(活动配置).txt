{
##########################################################################################

##########################################################################################

	'free_buy':{
		'show_info':{
			'pay_gold':['pay_gold_name','pay_gold_info','hero404'],	
			'pay_food':['pay_food_name','pay_food_info','hero403'],	
			'pay_wood':['pay_wood_name','pay_wood_info','hero401'],	
			'pay_iron':['pay_iron_name','pay_iron_info','hero405'],	
			'buy_gold':['buy_gold_name','buy_gold_info','hero408'],	
			'buy_food':['buy_food_name','buy_food_info','hero404'],	
			'buy_wood':['buy_wood_name','buy_wood_info','hero401'],	
			'buy_iron':['buy_iron_name','buy_iron_info','hero405'],	
			'baggage_gold':['baggage_gold_name','baggage_gold_info','hero403'],	
			'baggage_food':['baggage_food_name','baggage_food_info','hero401'],	
			'baggage_wood':['baggage_wood_name','baggage_wood_info','hero402'],	
			'baggage_iron':['baggage_iron_name','baggage_iron_info','hero405'],	
		},











































    '6':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,36000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,189000],1500],
                             [['buy',120,50,21000],1000],
                             [['baggage',60,20,2],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                     [['baggage',60,20,2],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,378000],1500],
                                  [['buy',120,50,42000],1000],
                                   [['baggage',60,20,2],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                   [['baggage',60,20,2],1000],
                                        ],
                        },
                         },

    '7':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,36000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,189000],1500],
                             [['buy',120,50,21000],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,378000],1500],
                                  [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '8':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,36000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,189000],1500],
                             [['buy',120,50,21000],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,378000],1500],
                                  [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '9':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,39000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,204750],1500],
                             [['buy',120,60,27300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,409500],1500],
                                  [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '10':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,39000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,204750],1500],
                             [['buy',120,60,27300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,409500],1500],
                                  [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '11':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,39000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,204750],1500],
                             [['buy',120,60,27300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,409500],1500],
                                  [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '12':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,42000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,220500],1500],
                             [['buy',120,70,34300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,441000],1500],
                                  [['buy',120,70,68600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '13':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,42000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,220500],1500],
                             [['buy',120,70,34300],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,441000],1500],
                                  [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '14':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,42000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,220500],1500],
                             [['buy',120,70,34300],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,441000],1500],
                                  [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '15':{
            'gold':{
                     'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                     'event':[
                             [['pay',120,60,45000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,236250],1500],
                             [['buy',120,80,42000],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                        'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                         'event':[
                                  [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,472500],1500],
                                  [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                           'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '16':{
            'gold':{
                     'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                     'event':[
                             [['pay',120,60,45000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,236250],1500],
                             [['buy',120,80,42000],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                        'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                         'event':[
                                  [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,472500],1500],
                                  [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                           'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '17':{
            'gold':{
                     'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                     'event':[
                             [['pay',120,60,45000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,236250],1500],
                             [['buy',120,80,42000],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                        'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                         'event':[
                                  [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,472500],1500],
                                  [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                           'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '18':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,48000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,252000],1500],
                             [['buy',120,90,50400],500],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],500],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,504000],1500],
                                  [['buy',120,90,100800],500],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],500],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '19':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,48000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,252000],1500],
                             [['buy',120,90,50400],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,504000],1500],
                                  [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '20':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,48000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,252000],1500],
                             [['buy',120,90,50400],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,504000],1500],
                                  [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '21':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,51000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,267750],1500],
                             [['buy',120,100,59500],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,535500],1500],
                                  [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '22':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,51000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,267750],1500],
                             [['buy',120,100,59500],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,535500],1500],
                                  [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '23':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,51000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,267750],1500],
                             [['buy',120,100,59500],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,535500],1500],
                                  [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '24':{
            'gold':{
                     'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                     'event':[
                             [['pay',120,60,54000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,283500],1000],
                             [['buy',120,110,69300],1000],
                             [['baggage',60,100,5],1000],
                             [['pay',120,680,765000],1500],
                             [['pay',120,1280,1728000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                        'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                     [['baggage',60,100,5],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                         'event':[
                                  [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,567000],1000],
                                  [['buy',120,110,138600],1000],
                                   [['baggage',60,100,5],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                           'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                   [['baggage',60,100,5],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                        ],
                        },
                         },

    '25':{
            'gold':{
                     'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                     'event':[
                             [['pay',120,60,54000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,283500],1000],
                             [['buy',120,110,69300],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,765000],1500],
                             [['pay',120,1280,1728000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                        'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                         'event':[
                                  [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,567000],1000],
                                  [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                           'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                        ],
                        },
                         },

    '26':{
            'gold':{
                     'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                     'event':[
                             [['pay',120,60,54000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,283500],1000],
                             [['buy',120,110,69300],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,765000],1500],
                             [['pay',120,1280,1728000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                        'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                         'event':[
                                  [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,567000],1000],
                                  [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                           'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                        ],
                        },
                         },

    '27':{
            'gold':{
                     'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                     'event':[
                             [['pay',120,60,57000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,299250],1000],
                             [['buy',120,120,79800],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,807500],1500],
                             [['pay',120,1280,1824000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                        'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                         'event':[
                                  [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,598500],1000],
                                  [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                           'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                        ],
                        },
                         },

    '28':{
            'gold':{
                     'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                     'event':[
                             [['pay',120,60,57000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,299250],1000],
                             [['buy',120,120,79800],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,807500],1500],
                             [['pay',120,1280,1824000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                        'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                         'event':[
                                  [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,598500],1000],
                                  [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                           'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                        ],
                        },
                         },

    '29':{
            'gold':{
                     'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                     'event':[
                             [['pay',120,60,57000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,299250],1000],
                             [['buy',120,120,79800],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,807500],1500],
                             [['pay',120,1280,1824000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                        'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                         'event':[
                                  [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,598500],1000],
                                  [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                           'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                        ],
                        },
                         },

    '30':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,60000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,315000],1000],
                             [['buy',120,130,91000],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,850000],1500],
                             [['pay',120,1280,1920000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,630000],1000],
                                  [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                        ],
                        },
                         },





	},

##########################购买政务########################
	'buy_gtask':{
		'name':'buy_gtask_name',
		'info':'buy_gtask_info',
		'hero':'hero761',
		'time':180,	#分钟
		'pay_money':30,	#需要充值的money
		'mulit':[1,3],	#居功至伟(reward_mulit)，3次
		'reward_key':'item021',	#黄金沙漏
		'para':[5,270,10],	#a为玩家完成第5次政务触发，if(玩家已完成政务数 < b && 全服最高政务数-玩家已完成政务数>c && 今日未触发过本事件)

	},


##########################购买武器########################
	'buy_weapon':{
		'name':'buy_weapon_name',
		'info':'buy_weapon_info',
		'hero':'hero420',
		'time':180,	#分钟
                         # 安装的宝物id   【充值金额，获得的奖励】
		'para':{'equip055':[150,{'equip':['equip075']}],
                        #当玩家装备成功equip055时弹出，只弹出一次'equip002':[150,{'item001':1,'item002':1,'item003':1}],
                 }
	},

	'day_buy_weapon':{
		'1':{
			'name':'day_buy_weapon_name',
			'info':'day_buy_weapon_info',
			'hero':'hero731',
			'open_day':[5,6],	#开放天数
                        'need_pay':300,	#需要充值的RMB
			'reward':{'equip':['equip076'],'item401':400,},	#方天画戟
		},
	},

##########################在线奖励########################
'act_online':{

	'pay_money':6,    #充值额度，大于等于此数量后，玩家可以领取充值奖励；		

	'online': [
		{
			'cd':10,    #分钟
			'reward':{'gold':10000,'item002':2},
			'pay_reward':{'item037':10,'item032':2},
		},
		{
			'cd':30,    #分钟
			'reward':{'gold':10000,'food':10000},
			'pay_reward':{'item037':10,'item032':3},
		},
		{
			'cd':60,    #分钟
			'reward':{'gold':10000,'wood':10000},
			'pay_reward':{'item037':10,'item032':5},
		},
		{
			'cd':120,    #分钟
			'reward':{'gold':10000,'iron':10000},
			'pay_reward':{'item037':10,'item032':10},
		},
	],
},


########################每日许愿############################
	'act_wish':{
		'wish_rewchoo_num':4,   #许愿奖励数量
		'wish_initrew':['coin',100],    #许愿初始奖励
		'wish_multiple':2,    #充值后奖励翻倍
		'hero_icon':'hero407',   #使用英雄图片
		'wish_rewpond':[
			['gold',5000],['gold',5000],['food',10000],['food',10000],['wood',10000],['wood',10000],['iron',10000],['iron',10000],['item032',5],['item037',5],['item021',1],['item024',1],['item027',2],['item057',10],
		],   ####许愿奖池
		'wish_days':7,    #许愿获得额外奖励的累计天数
		'wish_daysrew':[
                        {'item071':4,'item070':4},        #首次7天领取
                        {'item071':3,'item070':3,'item073':3,'item069':3,'item074':3},  
		],#按照数组顺序轮换领取，最后一组循环
	},
		
########################永久单笔充值############################
	'independ_pay':[
			{
				'pay_money':6,
				'reward':{'item087':1,'gold':50000,'item032':10,'item037':5,'item021':5,'equip':['equip055']},	
				'picture':['actPay1_1.png','actPay2_4.png', 'actPay3_4.png', 'icon_03.png'],
			},
			{
				'pay_money':30,
				'reward':{'item088':1,'gold':50000,'item032':10,'item037':5,'item021':5,'item402':20},
				'picture':['actPay1_2.png','actPay2_6.png', 'actPay3_6.png'],
			},
			{
				'pay_money':68,
				'reward':{'item087':1,'gold':50000,'item032':10,'item037':5,'item021':5,'item402':40},	
				'picture':['actPay1_1.png','actPay2_6.png', 'actPay3_7.png'],
			},
			{
				'pay_money':128,
				'reward':{'item088':1,'gold':50000,'item032':10,'item037':5,'item021':5,'item402':80},	
				'picture':['actPay1_2.png','actPay2_6.png', 'actPay3_6.png'],
			},
#			{
#				'delay_day':7,        #过7天以上才能见到入口
#				'pay_money':100,
#				'reward':{'item001':1,'item002':1,'item003':1,'item004':1,'item005':1,'item006':1},	
#				'picture':['act01','single_act01'],
#			},
#			{
#				'pay_money':100,
#				'reward':{'item001':1,'item002':1,'item003':1,'item004':1,'item005':1,'item006':1},	
#				'picture':['act01','single_act01'],
#			},

	],




######################加官进爵####################
	'act_officeup':{
		'pay_money':88,			#需要玩家累积充值的金额
		'officelv_reward':{
			1:{'coin':100},     #office等级,奖励
			2:{'coin':600}, 
			3:{'coin':1500}, 
			4:{'coin':2800}, 
			5:{'coin':3888}, 
		},
	},
	


##########################精彩活动###############
	#官邸升级，活动时间内累计充值获得的黄金
	#单笔充值，活动时间内记录单笔充值的rmb
	#累计充值，活动时间内累计充值获得的黄金

	'levelup':{
'reward':{








9:[6,{'item104':1,'gold':100000,'iron':200000,'wood':200000,'food':200000,}],
10:[6,{'item055':100,'gold':100000,'iron':200000,'wood':200000,'food':200000,}],
11:[30,{'item034':20000,'gold':150000,'iron':300000,'wood':300000,'food':300000,}],
12:[30,{'item059':100,'gold':150000,'iron':300000,'wood':300000,'food':300000,}],
13:[30,{'item057':100,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
14:[30,{'item036':10,'item079':200,'item080':200,'item081':200,'item082':200,}],
15:[30,{'item075':100,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
16:[30,{'item036':10,'item107':200,'item113':200,'item124':200,'item130':400,}],
17:[30,{'item070':30,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
18:[30,{'item071':30,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
19:[30,{'item059':200,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
20:[30,{'item057':200,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
21:[30,{'item061':50,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
22:[30,{'item036':20,'item406':200,'item407':200,'item408':200,'item409':200,}],
23:[30,{'item036':20,'item107':500,'item113':500,'item124':500,'item130':1000,}],
24:[30,{'item036':20,'item212':100,'item218':100,'item205':100,'item224':100,}],
25:[68,{'item036':20,'item191':10,'item189':50,'item030':20,'item187':50,}],
26:[68,{'item036':30,'item236':100,'item237':100,'item234':100,'item243':100,}],
27:[68,{'item036':30,'item189':100,'item603':5,'item271':100,'item238':100,}],
28:[128,{'item036':60,'item1030':20,'item292':200,'item291':200,'item280':200,}],
29:[128,{'item036':60,'item191':50,'item206':200,'item211':200,'item276':200,}],
30:[328,{'item036':120,'item1031':20,'item246':200,'item245':200,'item016':10,}],




},


		'time':10800,		#持续时间，秒
		'character':'hero704',	#立绘
		'title':'502003',		#标题
		'tips':'502004',		#提示文本
		'tab_name':'502009',		#提示文本
	},



################################################################
################################################################
########################单冲和累冲#################################
################################################################
################################################################
	'pay_ploy':{
		'msg_name': '502001',
		'msg_info': '502002',
		#'XX':{ o开头代表单笔充值，a开头代表累计充值，字母后面的数字向下延续，尽量不重复用10数内的id
			#'date':[[2018,9,1],[2018,10,1]],		#以年月日作为起始/结束日期,（eg：9月1号5点开始，10月1号5点结束）
			#'days':[a,b],		#a&b=开服第几天，a=开启天数，b=结束天数 （eg：偏移值5点，1号6点开服, 'days':[1, 2]   活动入口会在3号5点关闭）
			#'type':'once',		#活动类型：once单笔充值，addup累计充值，today每日累充，choice充值自选
			#'reward':{		#奖励库，根据活动类型写法不同
				#6:{'item003':1,'item004':2,'item005':10,'item020':25,'item073':10},		#单笔充值写法；金额：奖励
				#1000:{'item003':1,'item004':2,'item005':10,'item020':25,'item073':10},	#累计充值写法；金额：奖励
			#},
			#'character':'hero703',	#面板中的立绘
			#'title':'502005',		#标题，入口以及面板内
			#'tips':'502006',		#面板内的提示文本
		#},
      'a1':{         #初期资源
                        
                         'days':[1,6],'type':'addup',
                         'reward':{
                                      50:{'food':100000,'wood':100000,'iron':100000,},
                                      100:{'food':200000,'wood':200000,'iron':200000,'item030':5,'item021':5,},
                                      200:{'food':300000,'wood':300000,'iron':300000,'item030':10,'item021':10,},
                                      500:{'gold':200000,'food':400000,'wood':400000,'iron':400000,'item021':20,},
                                      1000:{'gold':300000,'food':600000,'wood':600000,'iron':600000,'item030':30,},
                                      2000:{'gold':500000,'food':1000000,'wood':1000000,'iron':1000000,'item021':50,},
                                      3000:{'gold':700000,'food':1400000,'wood':1400000,'iron':1400000,'item030':70,},
                                      5000:{'gold':1000000,'food':2000000,'wood':2000000,'iron':2000000,'item021':100,},
                                      
                                     },
                            'character':'hero723',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a2':{         #诸葛亮
                        
                         'days':[7,12],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item714':10,'item238':10,'item237':10,'item228':10,'gold':300000,},
                                      200:{'item714':20,'item229':20,'item235':20,'item232':20,'gold':600000,},
                                      500:{'item714':30,'item238':30,'item237':30,'item228':30,'gold':1000000,},
                                      1000:{'item714':40,'item229':40,'item235':40,'item232':40,'gold':1500000,},
                                      2000:{'item714':60,'item238':60,'item237':60,'item228':60,'gold':2100000,},
                                      3000:{'item714':80,'item229':80,'item235':80,'item232':80,'gold':2800000,},
                                      5000:{'item714':120,'item238':120,'item237':120,'item228':120,'gold':3600000,},
                                      6000:{'item036':100,'item229':160,'item235':160,'item232':160,'item224':100,},
                                      8000:{'item036':150,'item238':200,'item237':200,'item228':200,'item224':200,},
                                      10000:{'item036':200,'item229':240,'item235':240,'item232':240,'item224':360,},
                                     },
                            'character':'hero714',
                            'title':'502007',
                            'tips':'502008',
                            },












     'a3':{         #战争资源
                        
                         'days':[13,17],'type':'addup',
                         'reward':{
                                      50:{'gold':300000,'item027':5,'item030':5,},
                                      100:{'food':400000,'wood':200000,'item406':50,'item027':10,'item030':10,},
                                      200:{'gold':800000,'iron':400000,'item406':100,'item027':20,'item030':20,},
                                      500:{'food':1200000,'wood':600000,'item406':300,'item027':30,'item030':30,},
                                      1000:{'gold':2000000,'iron':1000000,'item035':1,'item027':40,'item030':40,},
                                      2000:{'food':3000000,'wood':1500000,'item035':2,'item027':50,'item030':50,},
                                      3000:{'gold':5000000,'iron':2500000,'item406':600,'item027':70,'item030':70,},
                                      5000:{'food':8000000,'wood':4000000,'item406':1000,'item027':100,'item030':100,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a4':{         #雷神套
                        
                         'days':[18,20],'type':'addup',
                         'show':[ 'equip034','equip038','equip041','equip043','equip048'],
                         'name':'502059',
                         'info':'502060',
                         'reward':{
                                      100:{'item414':40,'item406':200,'item036':10,'item021':10,'item409':200,},
                                      200:{'item414':80,'item406':400,'item036':20,'item021':20,'item409':400,},
                                      500:{'item415':40,'item414':160,'item036':30,'item021':30,'item407':200,},
                                      800:{'item415':80,'item414':320,'item036':40,'item021':40,'item407':400,},
                                      1200:{'item418':40,'item415':160,'item036':50,'item021':50,'item408':200,},
                                      2000:{'item418':80,'item415':320,'item036':60,'item021':60,'item408':400,},
                                      3000:{'item421':40,'item418':160,'item036':70,'item021':70,'item410':200,},
                                      5000:{'item421':80,'item418':320,'item036':80,'item021':80,'item410':400,},
                                      7500:{'item421':480,'item084':5,'item036':100,'item021':100,'item058':1000,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a5':{         #陆逊
                        
                         'days':[21,25],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item718':10,'item271':10,'item237':10,'item238':10,'gold':300000,},
                                      200:{'item718':20,'item271':20,'item233':20,'item235':20,'gold':600000,},
                                      500:{'item718':40,'item271':30,'item237':30,'item238':30,'gold':1000000,},
                                      1000:{'item718':60,'item271':40,'item233':40,'item235':40,'gold':1500000,},
                                      2000:{'item718':120,'item271':60,'item237':60,'item238':60,'gold':2100000,},
                                      3000:{'item718':150,'item271':80,'item233':80,'item235':80,'gold':2800000,},
                                      4000:{'item718':200,'item271':120,'item237':120,'item238':120,'gold':3600000,},
                                      6000:{'item036':100,'item271':240,'item233':240,'item235':240,'item205':100,},
                                      8000:{'item036':150,'item271':360,'item237':360,'item238':360,'item205':200,},
                                      10000:{'item036':200,'item271':480,'item233':480,'item235':480,'item205':360,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a6':{         #洗练
                        
                         'days':[26,28],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item406':100,'item407':100,'item410':50,'item056':30,},
                                      100:{'item036':10,'item409':100,'item408':100,'item410':50,'item056':60,},
                                      200:{'item036':20,'item406':200,'item407':200,'item410':100,'item056':90,},
                                      500:{'item036':30,'item409':200,'item408':200,'item410':100,'item056':120,},
                                      1000:{'item036':40,'item406':400,'item407':400,'item410':200,'item084':2,},
                                      2000:{'item036':60,'item409':400,'item408':400,'item410':200,'item084':4,},
                                      3000:{'item036':80,'item406':600,'item407':600,'item410':300,'item084':6,},
                                      5000:{'item036':120,'item409':600,'item408':600,'item410':300,'item084':10,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },
















     'a7':{         #祝融
                        
                         'days':[29,33],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item720':10,'item236':10,'item225':10,'item227':10,'gold':300000,},
                                      200:{'item720':20,'item236':20,'item226':20,'item234':20,'gold':600000,},
                                      500:{'item720':40,'item236':30,'item225':30,'item227':30,'gold':1000000,},
                                      1000:{'item720':60,'item236':40,'item226':40,'item234':40,'gold':1500000,},
                                      2000:{'item720':120,'item236':60,'item225':60,'item227':60,'gold':2100000,},
                                      3000:{'item720':150,'item236':80,'item226':80,'item234':80,'gold':2800000,},
                                      4000:{'item720':200,'item236':120,'item225':120,'item227':120,'gold':3600000,},
                                      6000:{'item036':100,'item236':240,'item226':240,'item234':240,'item290':100,},
                                      8000:{'item036':150,'item236':360,'item225':360,'item227':360,'item290':200,},
                                      10000:{'item036':200,'item236':480,'item226':480,'item234':480,'item290':360,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },















     'a8':{         #幻术无懈
                        
                         'days':[44,48],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item273':10,'item242':10,'item021':5,'item205':20,'item224':20,},
                                      200:{'item273':20,'item242':20,'item021':10,'item205':40,'item224':40,},
                                      500:{'item273':30,'item242':30,'item021':15,'item205':60,'item224':60,},
                                      1000:{'item273':40,'item242':40,'item021':20,'item205':80,'item224':80,},
                                      2000:{'item273':60,'item242':60,'item021':40,'item205':120,'item224':120,},
                                      3000:{'item273':80,'item242':80,'item035':1,'item205':160,'item224':160,},
                                      5000:{'item273':120,'item242':120,'item035':2,'item205':240,'item224':240,},
                                      
                                      
                                     },
                            'character':'hero720',
                            'title':'502007',
                            'tips':'502008',
                            },







     'a9':{         #英雄技能
                        
                         'days':[49,54],'type':'addup',
                         'reward':{
                                      50:{'item226':100,'item227':100,'item237':100,'item233':100,'item235':100,},
                                      100:{'item225':100,'item228':100,'item238':100,'item234':100,'item229':100,},
                                      200:{'item226':200,'item227':200,'item237':200,'item233':200,'item235':200,},
                                      500:{'item225':200,'item228':200,'item238':200,'item234':200,'item229':200,},
                                      1000:{'item226':300,'item227':300,'item237':300,'item233':300,'item235':300,},
                                      2000:{'item225':300,'item228':300,'item238':300,'item234':300,'item229':300,},
                                      3000:{'item226':500,'item227':500,'item237':500,'item233':500,'item235':500,},
                                      5000:{'item225':500,'item228':500,'item238':500,'item234':500,'item229':500,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },
















     'a10':{         #星辰
                        
                         'days':[55,60],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item601':5,'item605':5,},
                                      100:{'item036':10,'item601':10,'item605':10,'item027':10,'item030':10,},
                                      200:{'item036':20,'item602':5,'item606':5,'item027':20,'item030':20,},
                                      500:{'item036':30,'item602':10,'item606':10,'item027':30,'item030':30,},
                                      1000:{'item036':40,'item603':5,'item606':20,'item027':40,'item030':40,},
                                      2000:{'item036':60,'item603':10,'item606':30,'item027':50,'item030':50,},
                                      3000:{'item036':80,'item603':20,'item606':40,'item027':70,'item030':70,},
                                      5000:{'item036':120,'item603':40,'item606':60,'item027':100,'item030':100,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a11':{         #兵种技能
                        
                         'days':[61,66],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item218':100,'item205':100,'item217':150,'item217':150,},
                                      100:{'item036':10,'item224':100,'item212':100,'item223':150,'item223':150,},
                                      200:{'item036':20,'item218':200,'item205':200,'item217':300,'item217':300,},
                                      500:{'item036':30,'item224':200,'item212':200,'item223':300,'item223':300,},
                                      1000:{'item036':40,'item218':300,'item205':300,'item217':450,'item217':450,},
                                      2000:{'item036':60,'item224':300,'item212':300,'item223':450,'item223':450,},
                                      3000:{'item036':80,'item218':500,'item205':500,'item217':750,'item217':750,},
                                      5000:{'item036':120,'item224':500,'item212':500,'item223':750,'item223':750,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a12':{         #遗忘卷轴
                        
                         'days':[67,72],'type':'addup',
                         'reward':{
                                      50:{'gold':300000,'item027':5,'item030':5,},
                                      100:{'food':500000,'wood':250000,'item410':75,'item021':10,'item030':10,},
                                      200:{'gold':1000000,'iron':500000,'item410':150,'item021':20,'item030':20,},
                                      500:{'food':1500000,'wood':750000,'item410':300,'item021':30,'item030':30,},
                                      1000:{'gold':2500000,'iron':1250000,'item035':1,'item021':40,'item030':40,},
                                      2000:{'food':4500000,'wood':2250000,'item035':2,'item021':50,'item030':50,},
                                      3000:{'gold':7000000,'iron':3500000,'item035':3,'item021':70,'item030':70,},
                                      5000:{'food':10000000,'wood':5000000,'item035':4,'item021':100,'item030':100,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a13':{         #英雄技能
                        
                         'days':[73,78],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item230':100,'item234':100,'item236':100,'item238':100,},
                                      100:{'item036':10,'item231':100,'item233':100,'item237':100,'item232':100,},
                                      200:{'item036':20,'item230':200,'item234':200,'item236':200,'item238':200,},
                                      500:{'item036':30,'item231':200,'item233':200,'item237':200,'item232':200,},
                                      1000:{'item036':40,'item230':300,'item234':300,'item236':300,'item238':300,},
                                      2000:{'item036':60,'item231':300,'item233':300,'item237':300,'item232':300,},
                                      3000:{'item036':80,'item230':500,'item234':500,'item236':500,'item238':500,},
                                      5000:{'item036':120,'item231':500,'item233':500,'item237':500,'item232':500,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a14':{         #金手指
                        
                         'days':[79,84],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item406':100,'item407':100,'item410':50,'item056':30,},
                                      100:{'item036':10,'item409':100,'item408':100,'item410':50,'item056':60,},
                                      200:{'item036':20,'item406':200,'item407':200,'item410':100,'item056':90,},
                                      500:{'item036':30,'item409':200,'item408':200,'item410':100,'item056':120,},
                                      1000:{'item036':40,'item406':400,'item407':400,'item410':200,'item084':2,},
                                      2000:{'item036':60,'item409':400,'item408':400,'item410':200,'item084':4,},
                                      3000:{'item036':80,'item406':600,'item407':600,'item410':300,'item084':6,},
                                      5000:{'item036':120,'item409':600,'item408':600,'item410':300,'item084':10,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },















     'a15':{         #星辰
                        
                         'days':[85,90],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item601':5,'item605':5,},
                                      100:{'item036':10,'item601':10,'item605':10,'item021':10,'item030':10,},
                                      200:{'item036':20,'item602':5,'item606':5,'item021':20,'item030':20,},
                                      500:{'item036':30,'item602':10,'item606':10,'item021':30,'item030':30,},
                                      1000:{'item036':40,'item603':5,'item606':20,'item021':40,'item030':40,},
                                      2000:{'item036':60,'item603':10,'item606':30,'item021':50,'item030':50,},
                                      3000:{'item036':80,'item603':20,'item606':40,'item021':70,'item030':70,},
                                      5000:{'item036':120,'item603':40,'item606':60,'item021':100,'item030':100,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },













     'a16':{         #急救技能
                        
                         'days':[91,96],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item027':5,'item030':5,},
                                      100:{'item036':10,'item272':20,'item235':40,'item021':10,'item030':10,},
                                      200:{'item036':20,'item272':30,'item264':60,'item021':20,'item030':20,},
                                      500:{'item036':30,'item272':40,'item235':80,'item021':30,'item030':30,},
                                      1000:{'item036':40,'item272':60,'item264':120,'item021':40,'item030':40,},
                                      2000:{'item036':60,'item272':80,'item235':160,'item021':50,'item030':50,},
                                      3000:{'item036':80,'item272':100,'item264':200,'item021':70,'item030':70,},
                                      5000:{'item036':120,'item272':200,'item235':400,'item021':100,'item030':100,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },















     'a17':{         #星辰
                        
                         'days':[97,102],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item027':5,'item030':5,},
                                      100:{'item036':10,'item605':10,'item410':75,'item021':10,'item030':10,},
                                      200:{'item036':20,'item606':5,'item410':150,'item021':20,'item030':20,},
                                      500:{'item036':30,'item606':10,'item410':300,'item021':30,'item030':30,},
                                      1000:{'item036':40,'item606':20,'item035':1,'item021':40,'item030':40,},
                                      2000:{'item036':60,'item606':30,'item035':2,'item021':50,'item030':50,},
                                      3000:{'item036':80,'item606':40,'item035':3,'item021':70,'item030':70,},
                                      5000:{'item036':120,'item606':60,'item035':4,'item021':100,'item030':100,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },















     'a18':{         #枪王
                        
                         'days':[103,108],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item027':5,'item030':5,},
                                      100:{'item036':10,'item241':20,'item218':40,'item021':10,'item030':10,},
                                      200:{'item036':20,'item241':30,'item262':60,'item021':20,'item030':20,},
                                      500:{'item036':30,'item241':40,'item218':80,'item021':30,'item030':30,},
                                      1000:{'item036':40,'item241':60,'item262':120,'item021':40,'item030':40,},
                                      2000:{'item036':60,'item241':80,'item218':160,'item021':50,'item030':50,},
                                      3000:{'item036':80,'item241':100,'item262':200,'item021':70,'item030':70,},
                                      5000:{'item036':120,'item241':200,'item218':400,'item021':100,'item030':100,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a19':{         #洗练
                        
                         'days':[109,114],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item406':100,'item407':100,'item410':50,'item056':30,},
                                      100:{'item036':10,'item409':100,'item408':100,'item410':50,'item056':60,},
                                      200:{'item036':20,'item406':200,'item407':200,'item410':100,'item056':90,},
                                      500:{'item036':30,'item409':200,'item408':200,'item410':100,'item056':120,},
                                      1000:{'item036':40,'item406':400,'item407':400,'item410':200,'item084':2,},
                                      2000:{'item036':60,'item409':400,'item408':400,'item410':200,'item084':4,},
                                      3000:{'item036':80,'item406':600,'item407':600,'item410':300,'item084':6,},
                                      5000:{'item036':120,'item409':600,'item408':600,'item410':300,'item084':10,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a20':{         #技能
                        
                         'days':[115,120],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item027':5,'item030':5,},
                                      100:{'item036':10,'item273':20,'item205':40,'item021':10,'item030':10,},
                                      200:{'item036':20,'item273':30,'item265':60,'item021':20,'item030':20,},
                                      500:{'item036':30,'item273':40,'item205':80,'item021':30,'item030':30,},
                                      1000:{'item036':40,'item273':60,'item265':120,'item021':40,'item030':40,},
                                      2000:{'item036':60,'item273':80,'item205':160,'item021':50,'item030':50,},
                                      3000:{'item036':80,'item273':100,'item265':200,'item021':70,'item030':70,},
                                      5000:{'item036':120,'item273':200,'item205':400,'item021':100,'item030':100,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a21':{         #斗神套（真）
                        
                         'days':[34,38],'type':'addup',
                         'show':[ 'equip059','equip060','equip061','equip062','equip063'],
                         'name':'502069',
                         'info':'502070',
                         'reward':{
                                      50:{'item056':100,'item405':20,'item406':500,'item036':10,'item021':10,},
                                      100:{'equip':['equip059'],'item428':30,'item406':500,'item036':20,'item021':20,},
                                      200:{'equip':['equip060'],'item428':50,'item409':500,'item036':30,'item021':30,},
                                      500:{'equip':['equip061'],'item428':100,'item409':500,'item036':40,'item021':40,},
                                      1000:{'equip':['equip062'],'item428':150,'item407':1000,'item036':50,'item021':50,},
                                      1500:{'equip':['equip063'],'item428':200,'item408':1000,'item036':60,'item021':60,},
                                      2500:{'item409':1000,'item428':300,'item410':1000,'item036':70,'item021':70,},
                                      3500:{'item407':1000,'item428':450,'item084':1,'item036':80,'item021':80,},
                                      5000:{'item408':1000,'item428':600,'item084':2,'item036':100,'item021':100,},
                                      6500:{'item410':1000,'item428':800,'item084':3,'item036':120,'item021':120,},
                                     },
                            'character':'hero720',
                            'title':'502007',
                            'tips':'502008',
                            },















     'a22':{         #传奇技能预备
                        
                         'days':[39,43],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item241':10,'item272':10,'item021':5,'item235':20,'item218':20,},
                                      200:{'item241':20,'item272':20,'item021':10,'item235':40,'item218':40,},
                                      500:{'item241':30,'item272':30,'item021':15,'item235':60,'item218':60,},
                                      1000:{'item241':40,'item272':40,'item021':20,'item235':80,'item218':80,},
                                      2000:{'item241':60,'item272':60,'item021':40,'item235':120,'item218':120,},
                                      3000:{'item241':80,'item272':80,'item035':1,'item235':160,'item218':160,},
                                      5000:{'item241':120,'item272':120,'item035':2,'item235':240,'item218':240,},
                                      
                                      
                                     },
                            'character':'hero720',
                            'title':'502007',
                            'tips':'502008',
                            },















     'a23':{         #星辰
                        
                         'days':[121,126],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item601':5,'item605':5,},
                                      100:{'item036':10,'item601':10,'item605':10,'item021':10,'item030':10,},
                                      200:{'item036':20,'item602':5,'item606':5,'item021':20,'item030':20,},
                                      500:{'item036':30,'item602':10,'item606':10,'item021':30,'item030':30,},
                                      1000:{'item036':40,'item603':5,'item606':20,'item021':40,'item030':40,},
                                      2000:{'item036':60,'item603':10,'item606':30,'item021':50,'item030':50,},
                                      3000:{'item036':80,'item603':20,'item606':40,'item021':70,'item030':70,},
                                      5000:{'item036':120,'item603':40,'item606':60,'item021':100,'item030':100,},
                                      
                                     },
                            'character':'hero720',
                            'title':'502007',
                            'tips':'502008',
                            },
















     'a24':{         #急救枪王
                        
                         'days':[127,132],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item272':10,'item241':10,'item021':5,'item235':20,'item218':20,},
                                      200:{'item272':20,'item241':20,'item021':10,'item235':40,'item218':40,},
                                      500:{'item272':30,'item241':30,'item021':15,'item235':60,'item218':60,},
                                      1000:{'item272':40,'item241':40,'item021':20,'item235':80,'item218':80,},
                                      2000:{'item272':60,'item241':60,'item021':40,'item235':120,'item218':120,},
                                      3000:{'item272':80,'item241':80,'item035':1,'item235':160,'item218':160,},
                                      5000:{'item272':120,'item241':120,'item035':2,'item235':240,'item218':240,},
                                      
                                     },
                            'character':'hero720',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a25':{         #金手指
                        
                         'days':[133,138],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item406':100,'item407':100,'item410':50,'item056':30,},
                                      100:{'item036':10,'item409':100,'item408':100,'item410':50,'item056':60,},
                                      200:{'item036':20,'item406':200,'item407':200,'item410':100,'item056':90,},
                                      500:{'item036':30,'item409':200,'item408':200,'item410':100,'item056':120,},
                                      1000:{'item036':40,'item406':400,'item407':400,'item410':200,'item084':2,},
                                      2000:{'item036':60,'item409':400,'item408':400,'item410':200,'item084':4,},
                                      3000:{'item036':80,'item406':600,'item407':600,'item410':300,'item084':6,},
                                      5000:{'item036':120,'item409':600,'item408':600,'item410':300,'item084':10,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a26':{         #无懈幻术
                        
                         'days':[139,144],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item273':10,'item242':10,'item021':5,'item205':20,'item224':20,},
                                      200:{'item273':20,'item242':20,'item021':10,'item205':40,'item224':40,},
                                      500:{'item273':30,'item242':30,'item021':15,'item205':60,'item224':60,},
                                      1000:{'item273':40,'item242':40,'item021':20,'item205':80,'item224':80,},
                                      2000:{'item273':60,'item242':60,'item021':40,'item205':120,'item224':120,},
                                      3000:{'item273':80,'item242':80,'item035':1,'item205':160,'item224':160,},
                                      5000:{'item273':120,'item242':120,'item035':2,'item205':240,'item224':240,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a27':{         #诱敌
                        
                         'days':[145,150],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item271':10,'item237':10,'item238':10,'item229':10,'item228':10,},
                                      200:{'item271':20,'item233':20,'item235':20,'item232':20,'item233':20,},
                                      500:{'item271':30,'item237':30,'item238':30,'item229':30,'item228':30,},
                                      1000:{'item271':40,'item233':40,'item235':40,'item232':40,'item233':40,},
                                      2000:{'item271':60,'item237':60,'item238':60,'item229':60,'item228':60,},
                                      3000:{'item271':80,'item233':80,'item235':80,'item232':80,'item233':80,},
                                      5000:{'item271':120,'item237':120,'item238':120,'item229':120,'item228':120,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


     'a28':{         #星辰
                        
                         'days':[151,156],'type':'addup',
                         'reward':{
                                      50:{'item036':5,'item601':5,'item605':5,},
                                      100:{'item036':10,'item601':10,'item605':10,'item021':10,'item030':10,},
                                      200:{'item036':20,'item602':5,'item606':5,'item021':20,'item030':20,},
                                      500:{'item036':30,'item602':10,'item606':10,'item021':30,'item030':30,},
                                      1000:{'item036':40,'item603':5,'item606':20,'item021':40,'item030':40,},
                                      2000:{'item036':60,'item603':10,'item606':30,'item021':50,'item030':50,},
                                      3000:{'item036':80,'item603':20,'item606':40,'item021':70,'item030':70,},
                                      5000:{'item036':120,'item603':40,'item606':60,'item021':100,'item030':100,},
                                      
                                     },
                            'character':'hero720',
                            'title':'502007',
                            'tips':'502008',
                            },














     'a29':{         #诱敌
                        
                         'days':[157,162],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item271':10,'item237':10,'item238':10,'item229':10,'item228':10,},
                                      200:{'item271':20,'item233':20,'item235':20,'item232':20,'item233':20,},
                                      500:{'item271':30,'item237':30,'item238':30,'item229':30,'item228':30,},
                                      1000:{'item271':40,'item233':40,'item235':40,'item232':40,'item233':40,},
                                      2000:{'item271':60,'item237':60,'item238':60,'item229':60,'item228':60,},
                                      3000:{'item271':80,'item233':80,'item235':80,'item232':80,'item233':80,},
                                      5000:{'item271':120,'item237':120,'item238':120,'item229':120,'item228':120,},
                                      
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },














































































































































































     'o1':{      #初期资源
                        
                         'days':[1,2],'type':'once',
                         'reward':{
                                      6:{'wood':200000,'item003':3,'coin':60,},
                                      30:{'iron':300000,'item021':2,'item030':2,'item003':10,'coin':100,},
                                      68:{'food':500000,'item021':4,'item030':4,'item761':10,'coin':680,},
                                      128:{'gold':500000,'item021':6,'item030':6,'item761':20,'coin':1280,},
                                      328:{'item036':10,'item021':10,'item030':10,'item761':40,'coin':3280,},
                                      648:{'item036':20,'item021':20,'item030':20,'item761':80,'coin':6480,},
                                      998:{'item036':30,'item021':30,'item030':30,'item107':200,'coin':10000,},
                                      1998:{'item036':50,'item021':50,'item030':50,'item107':600,'coin':20000,},
                                     },
                            'character':'hero761',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o2':{      #周瑜
                        
                         'days':[3,4],'type':'once',
                         'reward':{
                                      6:{'item030':3,'item021':3,'coin':60,},
                                      30:{'item036':4,'item021':5,'item030':5,'item003':10,'coin':100,},
                                      68:{'item709':10,'item237':20,'item223':20,'item071':2,'coin':680,},
                                      128:{'item709':20,'item237':30,'item232':30,'item071':4,'coin':1280,},
                                      328:{'item709':40,'item237':40,'item223':40,'item071':6,'coin':3280,},
                                      648:{'item709':80,'item237':50,'item232':50,'item071':8,'coin':6480,},
                                      998:{'item709':100,'item237':80,'item223':80,'item071':10,'coin':10000,},
                                      1998:{'item709':120,'item237':120,'item232':120,'item071':15,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o3':{      #高级兵种技能
                        
                         'days':[5,6],'type':'once',
                         'reward':{
                                      6:{'item030':3,'item021':3,'coin':60,},
                                      30:{'item036':4,'item021':5,'item030':5,'coin':100,},
                                      68:{'item203':20,'item290':20,'item217':20,'item223':20,'coin':680,},
                                      128:{'item203':50,'item290':50,'item217':50,'item223':50,'coin':1280,},
                                      328:{'item203':150,'item290':150,'item217':150,'item223':150,'coin':3280,},
                                      648:{'item203':400,'item290':400,'item217':400,'item223':400,'coin':6480,},
                                      998:{'item201':600,'item207':600,'item036':30,'item021':60,'coin':10000,},
                                      1998:{'item204':800,'item208':800,'item036':50,'item021':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o4':{      #辅助技能
                        
                         'days':[7,8],'type':'once',
                         'reward':{
                                      6:{'item030':3,'item021':3,'coin':60,},
                                      30:{'item036':4,'item021':5,'item030':5,'coin':100,},
                                      68:{'gold':300000,'item270':20,'item260':20,'item261':20,'coin':680,},
                                      128:{'gold':500000,'item270':50,'item260':50,'item261':50,'coin':1280,},
                                      328:{'gold':1000000,'item270':150,'item260':150,'item261':150,'coin':3280,},
                                      648:{'gold':2000000,'item270':400,'item260':400,'item261':400,'coin':6480,},
                                      998:{'item036':30,'item030':60,'item056':400,'item027':60,'coin':10000,},
                                      1998:{'item036':50,'item030':100,'item056':800,'item027':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o5':{      #资源+星辰
                        
                         'days':[9,10],'type':'once',
                         'reward':{
                                      6:{'item030':3,'item021':3,'coin':60,},
                                      30:{'item036':4,'item021':5,'item030':5,'coin':100,},
                                      68:{'star1401':1,'item603':1,'item606':2,'item036':10,'coin':680,},
                                      128:{'star0801':1,'item603':2,'item606':4,'item036':20,'coin':1280,},
                                      328:{'star1201':1,'item603':3,'item606':6,'item036':30,'coin':3280,},
                                      648:{'star1001':1,'item603':4,'item606':8,'item036':50,'coin':6480,},
                                      998:{'item035':1,'item603':6,'item606':10,'item036':70,'coin':10000,},
                                      1998:{'item035':2,'item603':8,'item606':12,'item036':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o6':{      #羽林套
                        
                         'days':[11,12],'type':'once',
                         'show':[ 'equip070','equip071','equip072','equip073','equip074'],
                         'name':'502076',
                         'info':'502077',
                         'reward':{
                                      6:{'equip':['equip070'],'item401':20,'coin':60,},
                                      30:{'equip':['equip071'],'item402':40,'item401':20,'coin':100,},
                                      68:{'equip':['equip072'],'item403':60,'item402':20,'item401':60,'coin':680,},
                                      128:{'equip':['equip073'],'item404':80,'item403':20,'item402':40,'coin':1280,},
                                      328:{'equip':['equip074'],'item405':100,'item404':20,'item403':20,'coin':3280,},
                                      648:{'item237':400,'item236':400,'item231':400,'item232':400,'coin':6480,},
                                      998:{'item213':600,'item219':600,'item036':30,'item021':60,'coin':10000,},
                                      1998:{'item214':800,'item221':800,'item036':50,'item021':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o7':{      #宝物
                        
                         'days':[13,14],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item056':100,'item058':200,'item036':10,'item021':20,'coin':680,},
                                      128:{'item056':200,'item058':400,'item036':20,'item021':30,'coin':1280,},
                                      328:{'item056':300,'item058':600,'item036':30,'item021':40,'coin':3280,},
                                      648:{'item056':500,'item058':1000,'item036':50,'item021':50,'coin':6480,},
                                      998:{'item056':700,'item058':1500,'item075':50,'item087':5,'coin':10000,},
                                      1998:{'item056':1000,'item058':2000,'item075':100,'item087':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o8':{      #司马懿
                        
                         'days':[15,16],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item707':10,'item228':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item707':20,'item228':40,'item212':40,'item290':40,'coin':1280,},
                                      328:{'item707':50,'item228':100,'item212':100,'item290':100,'coin':3280,},
                                      648:{'item707':120,'item228':240,'item212':240,'item290':240,'coin':6480,},
                                      998:{'item707':140,'item228':400,'item212':400,'item290':400,'coin':10000,},
                                      1998:{'item707':160,'item228':600,'item212':600,'item290':600,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o9':{      #金刚和天命
                        
                         'days':[17,18],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item234':20,'item238':20,'item227':20,'item230':20,'coin':680,},
                                      128:{'item234':50,'item238':50,'item227':50,'item230':50,'coin':1280,},
                                      328:{'item234':150,'item238':150,'item227':150,'item230':150,'coin':3280,},
                                      648:{'item234':400,'item238':400,'item227':400,'item230':400,'coin':6480,},
                                      998:{'item201':600,'item207':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item204':800,'item208':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o10':{      #银币和幕府
                        
                         'days':[19,20],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'gold':1000000,'item059':100,'item021':20,'item034':10000,'coin':680,},
                                      128:{'gold':2000000,'item059':200,'item021':40,'item034':20000,'coin':1280,},
                                      328:{'gold':3000000,'item059':400,'item021':50,'item034':30000,'coin':3280,},
                                      648:{'gold':5000000,'item059':600,'item035':1,'item034':50000,'coin':6480,},
                                      998:{'gold':7000000,'item059':800,'item606':10,'item034':70000,'coin':10000,},
                                      1998:{'gold':10000000,'item059':1000,'item035':2,'item034':100000,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o11':{      #英雄技能
                        
                         'days':[21,22],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item235':20,'item233':20,'item229':20,'item225':20,'coin':680,},
                                      128:{'item235':50,'item233':50,'item229':50,'item225':50,'coin':1280,},
                                      328:{'item235':150,'item233':150,'item229':150,'item225':150,'coin':3280,},
                                      648:{'item235':400,'item233':400,'item229':400,'item225':400,'coin':6480,},
                                      998:{'item289':600,'item209':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item202':800,'item210':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o12':{      #兵种辅助
                        
                         'days':[23,24],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item205':20,'item212':20,'item268':20,'item269':20,'coin':680,},
                                      128:{'item205':50,'item212':50,'item268':50,'item269':50,'coin':1280,},
                                      328:{'item205':150,'item212':150,'item268':150,'item269':150,'coin':3280,},
                                      648:{'item205':400,'item212':400,'item268':400,'item269':400,'coin':6480,},
                                      998:{'item215':600,'item220':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item216':800,'item222':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o13':{      #金手指
                        
                         'days':[25,26],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item058':200,'item076':40,'item036':10,'item024':20,'coin':680,},
                                      128:{'item058':400,'item076':80,'item036':20,'item024':40,'coin':1280,},
                                      328:{'item084':1,'item076':120,'item036':40,'item024':80,'coin':3280,},
                                      648:{'item084':2,'item076':200,'item036':60,'item024':100,'coin':6480,},
                                      998:{'item084':3,'item056':200,'item036':80,'item024':120,'coin':10000,},
                                      1998:{'item084':5,'item056':300,'item036':100,'item024':160,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o14':{      #兵种辅助
                        
                         'days':[27,28],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item218':20,'item224':20,'item266':20,'item267':20,'coin':680,},
                                      128:{'item218':50,'item224':50,'item266':50,'item267':50,'coin':1280,},
                                      328:{'item218':150,'item224':150,'item266':150,'item267':150,'coin':3280,},
                                      648:{'item218':400,'item224':400,'item266':400,'item267':400,'coin':6480,},
                                      998:{'item088':5,'item030':60,'item058':800,'item034':70000,'coin':10000,},
                                      1998:{'item088':10,'item030':100,'item058':1200,'item034':100000,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o15':{      #智力技能
                        
                         'days':[29,30],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item238':20,'item263':10,'item229':20,'item205':20,'coin':680,},
                                      128:{'item238':50,'item263':30,'item229':50,'item205':50,'coin':1280,},
                                      328:{'item238':150,'item263':80,'item229':150,'item205':150,'coin':3280,},
                                      648:{'item238':400,'item263':200,'item229':400,'item205':400,'coin':6480,},
                                      998:{'item036':80,'item059':800,'item606':10,'item021':30,'coin':10000,},
                                      1998:{'item036':100,'item059':1000,'item606':15,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o16':{      #统帅技能
                        
                         'days':[31,32],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item228':20,'item265':10,'item233':20,'item212':20,'coin':680,},
                                      128:{'item228':50,'item265':30,'item233':50,'item212':50,'coin':1280,},
                                      328:{'item228':150,'item265':80,'item233':150,'item212':150,'coin':3280,},
                                      648:{'item228':400,'item265':200,'item233':400,'item212':400,'coin':6480,},
                                      998:{'item035':1,'item034':70000,'item030':60,'item107':1000,'coin':10000,},
                                      1998:{'item035':2,'item034':100000,'item030':100,'item107':1500,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o17':{      #璞玉
                        
                         'days':[33,34],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item405':50,'item401':200,'item403':200,'item036':10,'coin':680,},
                                      128:{'item405':100,'item402':200,'item404':200,'item036':15,'coin':1280,},
                                      328:{'item405':200,'item401':400,'item403':400,'item036':20,'coin':3280,},
                                      648:{'item405':400,'item402':400,'item404':400,'item036':30,'coin':6480,},
                                      998:{'item405':600,'item401':600,'item403':600,'item036':40,'coin':10000,},
                                      1998:{'item405':800,'item402':600,'item404':600,'item036':60,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o18':{      #星辰
                        
                         'days':[35,36],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item075':20,'item606':5,'item036':10,'item024':20,'coin':680,},
                                      128:{'item075':40,'item606':10,'item036':20,'item024':40,'coin':1280,},
                                      328:{'item075':80,'item606':15,'item036':40,'item024':80,'coin':3280,},
                                      648:{'item075':120,'item606':20,'item036':60,'item024':100,'coin':6480,},
                                      998:{'item603':4,'item606':15,'item036':80,'item024':120,'coin':10000,},
                                      1998:{'item603':8,'item606':20,'item036':100,'item024':150,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o19':{      #魅力技能
                        
                         'days':[37,38],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item237':20,'item264':10,'item235':20,'item218':20,'coin':680,},
                                      128:{'item237':50,'item264':30,'item235':50,'item218':50,'coin':1280,},
                                      328:{'item237':150,'item264':80,'item235':150,'item218':150,'coin':3280,},
                                      648:{'item237':400,'item264':200,'item235':400,'item218':400,'coin':6480,},
                                      998:{'item261':600,'item226':600,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item260':800,'item229':800,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o20':{      #武力技能
                        
                         'days':[39,40],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item236':20,'item262':10,'item225':20,'item224':20,'coin':680,},
                                      128:{'item236':50,'item262':30,'item225':50,'item224':50,'coin':1280,},
                                      328:{'item236':150,'item262':80,'item225':150,'item224':150,'coin':3280,},
                                      648:{'item236':400,'item262':200,'item225':400,'item224':400,'coin':6480,},
                                      998:{'item233':500,'item227':500,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item238':600,'item232':600,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o21':{      #职业文书
                        
                         'days':[41,42],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item066':25,'item067':25,'item068':25,'gold':200000,'coin':680,},
                                      128:{'item066':50,'item067':50,'item068':50,'gold':400000,'coin':1280,},
                                      328:{'item066':100,'item067':100,'item068':100,'gold':1000000,'coin':3280,},
                                      648:{'item066':200,'item067':200,'item068':200,'gold':3000000,'coin':6480,},
                                      998:{'item066':300,'item067':300,'item068':300,'gold':5000000,'coin':10000,},
                                      1998:{'item066':500,'item067':500,'item068':500,'gold':8000000,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o22':{      #步兵技能
                        
                         'days':[43,44],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item205':20,'item203':20,'item204':40,'item202':40,'coin':680,},
                                      128:{'item205':50,'item203':50,'item204':100,'item202':100,'coin':1280,},
                                      328:{'item205':150,'item203':150,'item204':300,'item202':300,'coin':3280,},
                                      648:{'item205':400,'item203':400,'item204':800,'item202':800,'coin':6480,},
                                      998:{'item270':500,'item234':500,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item235':600,'item267':600,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o23':{      #骑兵技能
                        
                         'days':[45,46],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item212':20,'item290':20,'item210':40,'item208':40,'coin':680,},
                                      128:{'item212':50,'item290':50,'item210':100,'item208':100,'coin':1280,},
                                      328:{'item212':150,'item290':150,'item210':300,'item208':300,'coin':3280,},
                                      648:{'item212':400,'item290':400,'item210':800,'item208':800,'coin':6480,},
                                      998:{'item230':500,'item236':500,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item268':600,'item237':600,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o24':{      #英雄碎片
                        
                         'days':[47,48],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item104':5,'item101':5,'item100':10,'gold':400000,'coin':680,},
                                      128:{'item104':10,'item101':10,'item100':20,'gold':800000,'coin':1280,},
                                      328:{'item104':20,'item101':20,'item100':40,'gold':2000000,'coin':3280,},
                                      648:{'item104':40,'item101':40,'item100':80,'gold':5000000,'coin':6480,},
                                      998:{'item104':60,'item101':60,'item100':120,'gold':8000000,'coin':10000,},
                                      1998:{'item104':80,'item101':80,'item100':160,'gold':12000000,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o25':{      #方士技能
                        
                         'days':[49,50],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item224':20,'item223':20,'item222':40,'item221':40,'coin':680,},
                                      128:{'item224':50,'item223':50,'item222':100,'item221':100,'coin':1280,},
                                      328:{'item224':150,'item223':150,'item222':300,'item221':300,'coin':3280,},
                                      648:{'item224':400,'item223':400,'item222':800,'item221':800,'coin':6480,},
                                      998:{'item056':500,'item058':1200,'item030':30,'item087':5,'coin':10000,},
                                      1998:{'item056':800,'item058':1500,'item030':50,'item087':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o26':{      #弓兵技能
                        
                         'days':[51,52],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item218':20,'item217':20,'item214':40,'item216':40,'coin':680,},
                                      128:{'item218':50,'item217':50,'item214':100,'item216':100,'coin':1280,},
                                      328:{'item218':150,'item217':150,'item214':300,'item216':300,'coin':3280,},
                                      648:{'item218':400,'item217':400,'item214':800,'item216':800,'coin':6480,},
                                      998:{'item036':80,'item059':800,'item606':10,'item021':30,'coin':10000,},
                                      1998:{'item036':100,'item059':1000,'item606':15,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o27':{      #金手指
                        
                         'days':[53,54],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item058':300,'item076':50,'item036':10,'item024':20,'coin':680,},
                                      128:{'item058':600,'item076':100,'item036':20,'item024':40,'coin':1280,},
                                      328:{'item084':2,'item076':150,'item036':40,'item024':80,'coin':3280,},
                                      648:{'item084':4,'item076':300,'item036':60,'item024':100,'coin':6480,},
                                      998:{'item084':6,'item606':15,'item036':80,'item024':120,'coin':10000,},
                                      1998:{'item084':8,'item606':20,'item036':100,'item024':160,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o28':{      #辅助技能
                        
                         'days':[55,56],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'gold':200000,'item270':20,'item260':20,'item261':20,'coin':680,},
                                      128:{'gold':400000,'item270':50,'item260':50,'item261':50,'coin':1280,},
                                      328:{'gold':1000000,'item270':150,'item260':150,'item261':150,'coin':3280,},
                                      648:{'gold':3000000,'item270':400,'item260':400,'item261':400,'coin':6480,},
                                      998:{'gold':7000000,'item058':1200,'item035':1,'item034':70000,'coin':10000,},
                                      1998:{'gold':10000000,'item058':1500,'item035':2,'item034':100000,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o29':{      #兵种文书
                        
                         'days':[57,58],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item062':25,'item063':25,'item064':25,'item065':25,'coin':680,},
                                      128:{'item062':50,'item063':50,'item064':50,'item065':50,'coin':1280,},
                                      328:{'item062':100,'item063':100,'item064':100,'item065':100,'coin':3280,},
                                      648:{'item062':200,'item063':200,'item064':200,'item065':200,'coin':6480,},
                                      998:{'item062':300,'item063':300,'item064':300,'item065':300,'coin':10000,},
                                      1998:{'item062':500,'item063':500,'item064':500,'item065':500,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o30':{      #英雄技能
                        
                         'days':[59,60],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item236':20,'item237':20,'item231':20,'item232':20,'coin':680,},
                                      128:{'item236':50,'item237':50,'item231':50,'item232':50,'coin':1280,},
                                      328:{'item236':150,'item237':150,'item231':150,'item232':150,'coin':3280,},
                                      648:{'item236':400,'item237':400,'item231':400,'item232':400,'coin':6480,},
                                      998:{'item213':600,'item219':600,'item036':30,'item021':60,'coin':10000,},
                                      1998:{'item214':800,'item221':800,'item036':50,'item021':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o31':{      #星辰
                        
                         'days':[61,62],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'star1401':1,'item603':1,'item606':2,'item036':10,'coin':680,},
                                      128:{'star0801':1,'item603':2,'item606':4,'item036':20,'coin':1280,},
                                      328:{'star1201':1,'item603':3,'item606':6,'item036':30,'coin':3280,},
                                      648:{'star1001':1,'item603':4,'item606':8,'item036':50,'coin':6480,},
                                      998:{'star0101':2,'item603':6,'item606':10,'item036':70,'coin':10000,},
                                      1998:{'star0201':2,'item603':8,'item606':12,'item036':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o32':{      #遗忘卷轴
                        
                         'days':[63,64],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item036':10,'item270':20,'item260':20,'item261':20,'coin':680,},
                                      128:{'item036':20,'item270':50,'item260':50,'item261':50,'coin':1280,},
                                      328:{'item035':1,'item270':150,'item260':150,'item261':150,'coin':3280,},
                                      648:{'item035':2,'item270':400,'item260':400,'item261':400,'coin':6480,},
                                      998:{'item035':3,'item030':60,'item058':400,'item027':60,'coin':10000,},
                                      1998:{'item035':4,'item030':100,'item058':800,'item027':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o33':{      #金手指
                        
                         'days':[65,66],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item056':50,'item058':100,'item036':10,'item021':20,'coin':680,},
                                      128:{'item056':100,'item058':200,'item036':20,'item021':30,'coin':1280,},
                                      328:{'item084':2,'item058':400,'item036':30,'item021':40,'coin':3280,},
                                      648:{'item084':4,'item058':600,'item036':50,'item021':50,'coin':6480,},
                                      998:{'item084':6,'item058':800,'item036':70,'item021':70,'coin':10000,},
                                      1998:{'item084':8,'item058':1200,'item036':100,'item021':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o34':{      #诱敌
                        
                         'days':[67,68],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item271':20,'item225':20,'item226':20,'item227':20,'coin':680,},
                                      128:{'item271':50,'item225':50,'item226':50,'item227':50,'coin':1280,},
                                      328:{'item271':150,'item225':150,'item226':150,'item227':150,'coin':3280,},
                                      648:{'item271':400,'item225':400,'item226':400,'item227':400,'coin':6480,},
                                      998:{'item271':600,'item225':600,'item226':600,'item227':600,'coin':10000,},
                                      1998:{'item271':800,'item225':800,'item226':800,'item227':800,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o35':{      #诱敌
                        
                         'days':[69,70],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item234':20,'item238':20,'item227':20,'item230':20,'coin':680,},
                                      128:{'item234':50,'item238':50,'item227':50,'item230':50,'coin':1280,},
                                      328:{'item234':150,'item238':150,'item227':150,'item230':150,'coin':3280,},
                                      648:{'item234':400,'item238':400,'item227':400,'item230':400,'coin':6480,},
                                      998:{'item201':600,'item207':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item204':800,'item208':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o36':{      #悲歌和乾坤
                        
                         'days':[71,72],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item235':20,'item233':20,'item229':20,'item225':20,'coin':680,},
                                      128:{'item235':50,'item233':50,'item229':50,'item225':50,'coin':1280,},
                                      328:{'item235':150,'item233':150,'item229':150,'item225':150,'coin':3280,},
                                      648:{'item235':400,'item233':400,'item229':400,'item225':400,'coin':6480,},
                                      998:{'item289':600,'item209':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item202':800,'item210':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o37':{      #
                        
                         'days':[73,74],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item205':20,'item212':20,'item268':20,'item269':20,'coin':680,},
                                      128:{'item205':50,'item212':50,'item268':50,'item269':50,'coin':1280,},
                                      328:{'item205':150,'item212':150,'item268':150,'item269':150,'coin':3280,},
                                      648:{'item205':400,'item212':400,'item268':400,'item269':400,'coin':6480,},
                                      998:{'item215':600,'item220':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item216':800,'item222':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o38':{      #兵种辅助
                        
                         'days':[75,76],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item218':20,'item224':20,'item266':20,'item267':20,'coin':680,},
                                      128:{'item218':50,'item224':50,'item266':50,'item267':50,'coin':1280,},
                                      328:{'item218':150,'item224':150,'item266':150,'item267':150,'coin':3280,},
                                      648:{'item218':400,'item224':400,'item266':400,'item267':400,'coin':6480,},
                                      998:{'item603':6,'item606':10,'item058':800,'item034':70000,'coin':10000,},
                                      1998:{'item603':8,'item606':12,'item058':1200,'item034':100000,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o39':{      #
                        
                         'days':[77,78],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item205':20,'item203':20,'item204':40,'item202':40,'coin':680,},
                                      128:{'item205':50,'item203':50,'item204':100,'item202':100,'coin':1280,},
                                      328:{'item205':150,'item203':150,'item204':300,'item202':300,'coin':3280,},
                                      648:{'item205':400,'item203':400,'item204':800,'item202':800,'coin':6480,},
                                      998:{'item270':500,'item234':500,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item235':600,'item267':600,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o40':{      #
                        
                         'days':[79,80],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item212':20,'item290':20,'item210':40,'item208':40,'coin':680,},
                                      128:{'item212':50,'item290':50,'item210':100,'item208':100,'coin':1280,},
                                      328:{'item212':150,'item290':150,'item210':300,'item208':300,'coin':3280,},
                                      648:{'item212':400,'item290':400,'item210':800,'item208':800,'coin':6480,},
                                      998:{'item230':500,'item236':500,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item268':600,'item237':600,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o41':{      #骑兵技能
                        
                         'days':[81,82],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item224':20,'item223':20,'item222':40,'item221':40,'coin':680,},
                                      128:{'item224':50,'item223':50,'item222':100,'item221':100,'coin':1280,},
                                      328:{'item224':150,'item223':150,'item222':300,'item221':300,'coin':3280,},
                                      648:{'item224':400,'item223':400,'item222':800,'item221':800,'coin':6480,},
                                      998:{'item237':500,'item266':500,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item238':600,'item228':600,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o42':{      #兵种技能
                        
                         'days':[83,84],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item218':20,'item217':20,'item214':40,'item216':40,'coin':680,},
                                      128:{'item218':50,'item217':50,'item214':100,'item216':100,'coin':1280,},
                                      328:{'item218':150,'item217':150,'item214':300,'item216':300,'coin':3280,},
                                      648:{'item218':400,'item217':400,'item214':800,'item216':800,'coin':6480,},
                                      998:{'item234':500,'item267':500,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item233':600,'item229':600,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o43':{      #遗忘卷轴
                        
                         'days':[85,86],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'gold':1000000,'item059':100,'item021':20,'item034':10000,'coin':680,},
                                      128:{'gold':2000000,'item059':200,'item021':40,'item034':20000,'coin':1280,},
                                      328:{'gold':3000000,'item059':400,'item021':50,'item034':30000,'coin':3280,},
                                      648:{'gold':5000000,'item059':600,'item035':1,'item034':50000,'coin':6480,},
                                      998:{'gold':7000000,'item059':800,'item606':10,'item034':70000,'coin':10000,},
                                      1998:{'gold':10000000,'item059':1000,'item035':2,'item034':100000,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o44':{      #英雄技能
                        
                         'days':[87,88],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item234':20,'item238':20,'item227':20,'item230':20,'coin':680,},
                                      128:{'item234':50,'item238':50,'item227':50,'item230':50,'coin':1280,},
                                      328:{'item234':150,'item238':150,'item227':150,'item230':150,'coin':3280,},
                                      648:{'item234':400,'item238':400,'item227':400,'item230':400,'coin':6480,},
                                      998:{'item201':600,'item207':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item204':800,'item208':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o45':{      #英雄技能
                        
                         'days':[89,90],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item235':20,'item233':20,'item229':20,'item225':20,'coin':680,},
                                      128:{'item235':50,'item233':50,'item229':50,'item225':50,'coin':1280,},
                                      328:{'item235':150,'item233':150,'item229':150,'item225':150,'coin':3280,},
                                      648:{'item235':400,'item233':400,'item229':400,'item225':400,'coin':6480,},
                                      998:{'item289':600,'item209':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item202':800,'item210':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o46':{      #
                        
                         'days':[91,92],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item236':20,'item237':20,'item231':20,'item232':20,'coin':680,},
                                      128:{'item236':50,'item237':50,'item231':50,'item232':50,'coin':1280,},
                                      328:{'item236':150,'item237':150,'item231':150,'item232':150,'coin':3280,},
                                      648:{'item236':400,'item237':400,'item231':400,'item232':400,'coin':6480,},
                                      998:{'item213':600,'item219':600,'item036':30,'item021':60,'coin':10000,},
                                      1998:{'item214':800,'item221':800,'item036':50,'item021':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o47':{      #
                        
                         'days':[93,94],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item075':20,'item606':5,'item036':10,'item024':20,'coin':680,},
                                      128:{'item075':40,'item606':10,'item036':20,'item024':40,'coin':1280,},
                                      328:{'item075':80,'item606':15,'item036':40,'item024':80,'coin':3280,},
                                      648:{'item075':120,'item606':20,'item036':60,'item024':100,'coin':6480,},
                                      998:{'item603':4,'item606':15,'item036':80,'item024':120,'coin':10000,},
                                      1998:{'item603':8,'item606':20,'item036':100,'item024':150,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o48':{      #
                        
                         'days':[95,96],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item056':50,'item058':100,'item036':10,'item021':20,'coin':680,},
                                      128:{'item056':100,'item058':200,'item036':20,'item021':30,'coin':1280,},
                                      328:{'item084':2,'item058':400,'item036':30,'item021':40,'coin':3280,},
                                      648:{'item084':4,'item058':600,'item036':50,'item021':50,'coin':6480,},
                                      998:{'item084':6,'item058':800,'item036':70,'item021':70,'coin':10000,},
                                      1998:{'item084':8,'item058':1200,'item036':100,'item021':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o49':{      #
                        
                         'days':[97,98],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item205':20,'item212':20,'item268':20,'item269':20,'coin':680,},
                                      128:{'item205':50,'item212':50,'item268':50,'item269':50,'coin':1280,},
                                      328:{'item205':150,'item212':150,'item268':150,'item269':150,'coin':3280,},
                                      648:{'item205':400,'item212':400,'item268':400,'item269':400,'coin':6480,},
                                      998:{'item215':600,'item220':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item216':800,'item222':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o50':{      #
                        
                         'days':[99,100],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item218':20,'item224':20,'item266':20,'item267':20,'coin':680,},
                                      128:{'item218':50,'item224':50,'item266':50,'item267':50,'coin':1280,},
                                      328:{'item218':150,'item224':150,'item266':150,'item267':150,'coin':3280,},
                                      648:{'item218':400,'item224':400,'item266':400,'item267':400,'coin':6480,},
                                      998:{'item204':600,'item210':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item202':800,'item208':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o51':{      #
                        
                         'days':[101,102],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item036':10,'item270':20,'item260':20,'item261':20,'coin':680,},
                                      128:{'item036':20,'item270':50,'item260':50,'item261':50,'coin':1280,},
                                      328:{'item035':1,'item270':150,'item260':150,'item261':150,'coin':3280,},
                                      648:{'item035':2,'item270':400,'item260':400,'item261':400,'coin':6480,},
                                      998:{'item035':3,'item030':60,'item058':400,'item027':60,'coin':10000,},
                                      1998:{'item035':4,'item030':100,'item058':800,'item027':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o52':{      #
                        
                         'days':[103,104],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item238':20,'item263':10,'item229':20,'item205':20,'coin':680,},
                                      128:{'item238':50,'item263':30,'item229':50,'item205':50,'coin':1280,},
                                      328:{'item238':150,'item263':80,'item229':150,'item205':150,'coin':3280,},
                                      648:{'item238':400,'item263':200,'item229':400,'item205':400,'coin':6480,},
                                      998:{'item036':80,'item059':800,'item606':10,'item021':30,'coin':10000,},
                                      1998:{'item036':100,'item059':1000,'item606':15,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o53':{      #
                        
                         'days':[105,106],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item228':20,'item265':10,'item233':20,'item212':20,'coin':680,},
                                      128:{'item228':50,'item265':30,'item233':50,'item212':50,'coin':1280,},
                                      328:{'item228':150,'item265':80,'item233':150,'item212':150,'coin':3280,},
                                      648:{'item228':400,'item265':200,'item233':400,'item212':400,'coin':6480,},
                                      998:{'item035':1,'item034':70000,'item030':60,'item107':1000,'coin':10000,},
                                      1998:{'item035':2,'item034':100000,'item030':100,'item107':1500,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o54':{      #
                        
                         'days':[107,108],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item056':50,'item058':100,'item036':10,'item021':20,'coin':680,},
                                      128:{'item056':100,'item058':200,'item036':20,'item021':30,'coin':1280,},
                                      328:{'item084':2,'item058':400,'item036':30,'item021':40,'coin':3280,},
                                      648:{'item084':4,'item058':600,'item036':50,'item021':50,'coin':6480,},
                                      998:{'item084':6,'item058':800,'item036':70,'item021':70,'coin':10000,},
                                      1998:{'item084':8,'item058':1200,'item036':100,'item021':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o55':{      #
                        
                         'days':[109,110],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item237':20,'item264':10,'item235':20,'item218':20,'coin':680,},
                                      128:{'item237':50,'item264':30,'item235':50,'item218':50,'coin':1280,},
                                      328:{'item237':150,'item264':80,'item235':150,'item218':150,'coin':3280,},
                                      648:{'item237':400,'item264':200,'item235':400,'item218':400,'coin':6480,},
                                      998:{'item261':600,'item226':600,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item260':800,'item229':800,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o56':{      #
                        
                         'days':[111,112],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item236':20,'item262':10,'item225':20,'item224':20,'coin':680,},
                                      128:{'item236':50,'item262':30,'item225':50,'item224':50,'coin':1280,},
                                      328:{'item236':150,'item262':80,'item225':150,'item224':150,'coin':3280,},
                                      648:{'item236':400,'item262':200,'item225':400,'item224':400,'coin':6480,},
                                      998:{'item233':500,'item227':500,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item238':600,'item232':600,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o57':{      #步兵技能
                        
                         'days':[113,114],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item205':20,'item203':20,'item204':40,'item202':40,'coin':680,},
                                      128:{'item205':50,'item203':50,'item204':100,'item202':100,'coin':1280,},
                                      328:{'item205':150,'item203':150,'item204':300,'item202':300,'coin':3280,},
                                      648:{'item205':400,'item203':400,'item204':800,'item202':800,'coin':6480,},
                                      998:{'item270':500,'item234':500,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item235':600,'item267':600,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o58':{      #骑兵技能
                        
                         'days':[115,116],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item212':20,'item290':20,'item210':40,'item208':40,'coin':680,},
                                      128:{'item212':50,'item290':50,'item210':100,'item208':100,'coin':1280,},
                                      328:{'item212':150,'item290':150,'item210':300,'item208':300,'coin':3280,},
                                      648:{'item212':400,'item290':400,'item210':800,'item208':800,'coin':6480,},
                                      998:{'item230':500,'item236':500,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item268':600,'item237':600,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o59':{      #金手指
                        
                         'days':[117,118],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item058':300,'item076':50,'item036':10,'item024':20,'coin':680,},
                                      128:{'item058':600,'item076':100,'item036':20,'item024':40,'coin':1280,},
                                      328:{'item084':2,'item076':150,'item036':40,'item024':80,'coin':3280,},
                                      648:{'item084':4,'item076':300,'item036':60,'item024':100,'coin':6480,},
                                      998:{'item084':6,'item606':15,'item036':80,'item024':120,'coin':10000,},
                                      1998:{'item084':8,'item606':20,'item036':100,'item024':160,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o60':{      #方士技能
                        
                         'days':[119,120],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item224':20,'item223':20,'item222':40,'item221':40,'coin':680,},
                                      128:{'item224':50,'item223':50,'item222':100,'item221':100,'coin':1280,},
                                      328:{'item224':150,'item223':150,'item222':300,'item221':300,'coin':3280,},
                                      648:{'item224':400,'item223':400,'item222':800,'item221':800,'coin':6480,},
                                      998:{'item056':500,'item058':1200,'item030':30,'item087':5,'coin':10000,},
                                      1998:{'item056':800,'item058':1500,'item030':50,'item087':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o61':{      #弓兵技能
                        
                         'days':[121,122],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item218':20,'item217':20,'item214':40,'item216':40,'coin':680,},
                                      128:{'item218':50,'item217':50,'item214':100,'item216':100,'coin':1280,},
                                      328:{'item218':150,'item217':150,'item214':300,'item216':300,'coin':3280,},
                                      648:{'item218':400,'item217':400,'item214':800,'item216':800,'coin':6480,},
                                      998:{'item036':80,'item059':800,'item606':10,'item021':30,'coin':10000,},
                                      1998:{'item036':100,'item059':1000,'item606':15,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o62':{      #高级兵种技能
                        
                         'days':[123,124],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item203':20,'item290':20,'item217':20,'item223':20,'coin':680,},
                                      128:{'item203':50,'item290':50,'item217':50,'item223':50,'coin':1280,},
                                      328:{'item203':150,'item290':150,'item217':150,'item223':150,'coin':3280,},
                                      648:{'item203':400,'item290':400,'item217':400,'item223':400,'coin':6480,},
                                      998:{'item201':600,'item207':600,'item036':30,'item021':60,'coin':10000,},
                                      1998:{'item204':800,'item208':800,'item036':50,'item021':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o63':{      #辅助技能
                        
                         'days':[125,126],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'gold':300000,'item270':20,'item260':20,'item261':20,'coin':680,},
                                      128:{'gold':500000,'item270':50,'item260':50,'item261':50,'coin':1280,},
                                      328:{'gold':1000000,'item270':150,'item260':150,'item261':150,'coin':3280,},
                                      648:{'gold':2000000,'item270':400,'item260':400,'item261':400,'coin':6480,},
                                      998:{'item036':30,'item030':60,'item056':400,'item027':60,'coin':10000,},
                                      1998:{'item036':50,'item030':100,'item056':800,'item027':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o64':{      #英雄技能
                        
                         'days':[127,128],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':5,'coin':100,},
                                      68:{'item236':20,'item237':20,'item231':20,'item232':20,'coin':680,},
                                      128:{'item236':50,'item237':50,'item231':50,'item232':50,'coin':1280,},
                                      328:{'item236':150,'item237':150,'item231':150,'item232':150,'coin':3280,},
                                      648:{'item236':400,'item237':400,'item231':400,'item232':400,'coin':6480,},
                                      998:{'item213':600,'item219':600,'item036':30,'item021':60,'coin':10000,},
                                      1998:{'item214':800,'item221':800,'item036':50,'item021':100,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o65':{      #金刚和天命
                        
                         'days':[129,130],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item234':20,'item238':20,'item227':20,'item230':20,'coin':680,},
                                      128:{'item234':50,'item238':50,'item227':50,'item230':50,'coin':1280,},
                                      328:{'item234':150,'item238':150,'item227':150,'item230':150,'coin':3280,},
                                      648:{'item234':400,'item238':400,'item227':400,'item230':400,'coin':6480,},
                                      998:{'item201':600,'item207':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item204':800,'item208':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o66':{      #英雄技能
                        
                         'days':[131,132],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item235':20,'item233':20,'item229':20,'item225':20,'coin':680,},
                                      128:{'item235':50,'item233':50,'item229':50,'item225':50,'coin':1280,},
                                      328:{'item235':150,'item233':150,'item229':150,'item225':150,'coin':3280,},
                                      648:{'item235':400,'item233':400,'item229':400,'item225':400,'coin':6480,},
                                      998:{'item289':600,'item209':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item202':800,'item210':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o67':{      #兵种辅助
                        
                         'days':[133,134],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item205':20,'item212':20,'item268':20,'item269':20,'coin':680,},
                                      128:{'item205':50,'item212':50,'item268':50,'item269':50,'coin':1280,},
                                      328:{'item205':150,'item212':150,'item268':150,'item269':150,'coin':3280,},
                                      648:{'item205':400,'item212':400,'item268':400,'item269':400,'coin':6480,},
                                      998:{'item215':600,'item220':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item216':800,'item222':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o68':{      #金手指
                        
                         'days':[135,136],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item076':20,'item058':200,'item036':10,'item024':20,'coin':680,},
                                      128:{'item076':40,'item058':400,'item036':20,'item024':40,'coin':1280,},
                                      328:{'item084':1,'item058':600,'item036':40,'item024':80,'coin':3280,},
                                      648:{'item084':2,'item058':800,'item036':60,'item024':100,'coin':6480,},
                                      998:{'item084':3,'item058':1000,'item036':80,'item024':120,'coin':10000,},
                                      1998:{'item084':4,'item058':1200,'item036':100,'item024':160,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o69':{      #兵种辅助
                        
                         'days':[137,138],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item218':20,'item224':20,'item266':20,'item267':20,'coin':680,},
                                      128:{'item218':50,'item224':50,'item266':50,'item267':50,'coin':1280,},
                                      328:{'item218':150,'item224':150,'item266':150,'item267':150,'coin':3280,},
                                      648:{'item218':400,'item224':400,'item266':400,'item267':400,'coin':6480,},
                                      998:{'item088':5,'item030':60,'item058':800,'item034':70000,'coin':10000,},
                                      1998:{'item088':10,'item030':100,'item058':1200,'item034':100000,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o70':{      #智力技能
                        
                         'days':[139,140],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item238':20,'item263':10,'item229':20,'item205':20,'coin':680,},
                                      128:{'item238':50,'item263':30,'item229':50,'item205':50,'coin':1280,},
                                      328:{'item238':150,'item263':80,'item229':150,'item205':150,'coin':3280,},
                                      648:{'item238':400,'item263':200,'item229':400,'item205':400,'coin':6480,},
                                      998:{'item036':80,'item030':60,'item606':10,'item021':30,'coin':10000,},
                                      1998:{'item036':100,'item030':100,'item606':15,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o71':{      #统帅技能
                        
                         'days':[141,142],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item228':20,'item265':10,'item233':20,'item212':20,'coin':680,},
                                      128:{'item228':50,'item265':30,'item233':50,'item212':50,'coin':1280,},
                                      328:{'item228':150,'item265':80,'item233':150,'item212':150,'coin':3280,},
                                      648:{'item228':400,'item265':200,'item233':400,'item212':400,'coin':6480,},
                                      998:{'item035':1,'item034':70000,'item030':60,'item107':1000,'coin':10000,},
                                      1998:{'item035':2,'item034':100000,'item030':100,'item107':1500,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o72':{      #魅力技能
                        
                         'days':[143,144],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item237':20,'item264':10,'item235':20,'item218':20,'coin':680,},
                                      128:{'item237':50,'item264':30,'item235':50,'item218':50,'coin':1280,},
                                      328:{'item237':150,'item264':80,'item235':150,'item218':150,'coin':3280,},
                                      648:{'item237':400,'item264':200,'item235':400,'item218':400,'coin':6480,},
                                      998:{'item261':600,'item226':600,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item260':800,'item229':800,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o73':{      #武力技能
                        
                         'days':[145,146],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item236':20,'item262':10,'item225':20,'item224':20,'coin':680,},
                                      128:{'item236':50,'item262':30,'item225':50,'item224':50,'coin':1280,},
                                      328:{'item236':150,'item262':80,'item225':150,'item224':150,'coin':3280,},
                                      648:{'item236':400,'item262':200,'item225':400,'item224':400,'coin':6480,},
                                      998:{'item233':500,'item227':500,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item238':600,'item232':600,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o74':{      #
                        
                         'days':[147,148],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item237':20,'item264':10,'item235':20,'item218':20,'coin':680,},
                                      128:{'item237':50,'item264':30,'item235':50,'item218':50,'coin':1280,},
                                      328:{'item237':150,'item264':80,'item235':150,'item218':150,'coin':3280,},
                                      648:{'item237':400,'item264':200,'item235':400,'item218':400,'coin':6480,},
                                      998:{'item261':600,'item226':600,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item260':800,'item229':800,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o75':{      #
                        
                         'days':[149,150],'type':'once',
                         'reward':{
                                      6:{'item036':3,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item236':20,'item262':10,'item225':20,'item224':20,'coin':680,},
                                      128:{'item236':50,'item262':30,'item225':50,'item224':50,'coin':1280,},
                                      328:{'item236':150,'item262':80,'item225':150,'item224':150,'coin':3280,},
                                      648:{'item236':400,'item262':200,'item225':400,'item224':400,'coin':6480,},
                                      998:{'item233':500,'item227':500,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item238':600,'item232':600,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o76':{      #统帅技能
                        
                         'days':[151,152],'type':'once',
                         'reward':{
                                      6:{'item036':3,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item228':20,'item265':10,'item233':20,'item212':20,'coin':680,},
                                      128:{'item228':50,'item265':30,'item233':50,'item212':50,'coin':1280,},
                                      328:{'item228':150,'item265':80,'item233':150,'item212':150,'coin':3280,},
                                      648:{'item228':400,'item265':200,'item233':400,'item212':400,'coin':6480,},
                                      998:{'item035':1,'item034':70000,'item030':60,'item107':1000,'coin':10000,},
                                      1998:{'item035':2,'item034':100000,'item030':100,'item107':1500,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o77':{      #魅力技能
                        
                         'days':[153,154],'type':'once',
                         'reward':{
                                      6:{'item036':3,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item237':20,'item264':10,'item235':20,'item218':20,'coin':680,},
                                      128:{'item237':50,'item264':30,'item235':50,'item218':50,'coin':1280,},
                                      328:{'item237':150,'item264':80,'item235':150,'item218':150,'coin':3280,},
                                      648:{'item237':400,'item264':200,'item235':400,'item218':400,'coin':6480,},
                                      998:{'item261':600,'item226':600,'item036':30,'item030':30,'coin':10000,},
                                      1998:{'item260':800,'item229':800,'item036':50,'item030':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o78':{      #武力技能
                        
                         'days':[155,156],'type':'once',
                         'reward':{
                                      6:{'item036':3,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item236':20,'item262':10,'item225':20,'item224':20,'coin':680,},
                                      128:{'item236':50,'item262':30,'item225':50,'item224':50,'coin':1280,},
                                      328:{'item236':150,'item262':80,'item225':150,'item224':150,'coin':3280,},
                                      648:{'item236':400,'item262':200,'item225':400,'item224':400,'coin':6480,},
                                      998:{'item233':500,'item227':500,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item238':600,'item232':600,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o79':{      #英雄技能
                        
                         'days':[157,158],'type':'once',
                         'reward':{
                                      6:{'item036':2,'item021':3,'coin':60,},
                                      30:{'item036':4,'item021':5,'item030':5,'coin':100,},
                                      68:{'item234':20,'item238':20,'item227':20,'item230':20,'coin':680,},
                                      128:{'item234':50,'item238':50,'item227':50,'item230':50,'coin':1280,},
                                      328:{'item234':150,'item238':150,'item227':150,'item230':150,'coin':3280,},
                                      648:{'item234':400,'item238':400,'item227':400,'item230':400,'coin':6480,},
                                      998:{'item201':600,'item207':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item204':800,'item208':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o80':{      #英雄技能
                        
                         'days':[159,160],'type':'once',
                         'reward':{
                                      6:{'item036':2,'item021':3,'coin':60,},
                                      30:{'item036':4,'item021':5,'item030':5,'coin':100,},
                                      68:{'item235':20,'item233':20,'item229':20,'item225':20,'coin':680,},
                                      128:{'item235':50,'item233':50,'item229':50,'item225':50,'coin':1280,},
                                      328:{'item235':150,'item233':150,'item229':150,'item225':150,'coin':3280,},
                                      648:{'item235':400,'item233':400,'item229':400,'item225':400,'coin':6480,},
                                      998:{'item289':600,'item209':600,'item036':30,'item021':30,'coin':10000,},
                                      1998:{'item202':800,'item210':800,'item036':50,'item021':50,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },




	},
	'week_card':{		#周卡活动
		'pay':30,		#需要单笔充值的金额，单位：RMB
		#'pay_reward':300,		#立即获得300黄金，单位：黄金
		'cycle':7,		#有奖励的周期，单位：天/偏移时间点
		'cycle_reward':400,		#周期奖励，单位：黄金
		#购买周卡后，立即获得300黄金，购买之后每天可以获得周期奖励，周期奖励是从第二天开始算的，每过一个偏移时间，可领取一次，一共领取7次。
		#第七次的奖励被领取的那天，弹出新一期的周卡奖励
		#玩家没在奖励周期时显示购买入口，玩家在奖励周期内不显示购买入口
		#玩家在奖励周期内时，每天第一次登陆时，发给玩家本日的黄金奖励，登录时推送固定界面
		#若玩家在奖励周期内，没有每天都登陆，未登陆的天数不做计数，即玩家可以完整领取所有7次的奖励
	},
	'member_card':{		#永久卡活动
		'pay':288,		#需要单笔充值的金额，单位：RMB
		'show_pay':6,		#充6元可见
		'day_reward':{'coin':200,'gold':50000,'food':100000,'wood':100000,'iron':100000,'item070':1,'item071':1, 'item021':5},		#每日赠送的奖励
		'limit_count':10,		#奖励最多累积的天数
		'help_info':'member_03', # 帮助
		#购买（或激活）永久卡后，立即获得Min（开服天数,'limit_count'）*'day_reward'奖励
		#之后每一天登录的角色皆可领取一份'day_reward'
		#玩家购买之后立即激活当前服永久卡，在其他区需要手动激活永久卡

	},
	'fund':{		#超级基金，
		'day_count':1,		#天数计算规则，0=按玩家购买天数，1=开服天数
		'need_pay_money':200,		#购买此功能的资格，需要已经累计充值的rmb，单位：rmb
		'buy':800,		#购买此功能的消耗，单位：黄金
		'show':36000,		#显示数量
		'image':'hero761',		#立绘人物
		'reward':[		#数组内每一个奖励对应一个天数，每天只可领取一次奖励
			{'coin':500},		#黄金500
			{'item087':2},		#合击箱2
			{'item088':2},		#宿命箱2
			{'coin':300},		#黄金300
			{'item027':20},		
			{'item056':100},	#高宝箱100
			{'item087':10},		
			{'item203':20},		
			{'item217':20},	
			{'item088':10},		
			{'item290':20},		#突击20
			{'item223':20},		
			{'coin':300},		#黄金300
			{'item036':10},		#辎重10
			{'coin':300},		#黄金300
			{'item056':150},		
			{'coin':300},	
			{'item058':200},		#高洗箱200
			{'item058':300},		#高洗箱300
			{'coin':800},		#黄金800
		],
		#这个活动每个玩家只可参与一次，开服当前就开启，玩家完成后永久隐藏该活动的入口，领取完所有奖励就算完成，完成这个活动后，就算更改配置条目也不会再出现
		#购买该功能的当天就可以领取第一份奖励，之后的奖励按自然日激活领取功能
		#玩家在奖励周期内，是否登录游戏不影响玩家领取奖励的进度，例：开服当天购买该功能，但之后2天都没登录，第四天登录时，也可以领取前两日的奖励
		#若该功能内仍有奖励未领取，则一直显示功能入口
	},





############折扣商店#####################
'act_sale_shop':{    #开服后第N天开启，当天可见（同一天只会有一个）
  'begin_time':[12,0],  #开启后持续过当天
  'open_days':{3:'goods1',4:'goods1',5:'goods1',7:'goods2',8:'goods2',9:'goods2',13:'goods3',14:'goods3',15:'goods3',18:'goods4',19:'goods4',20:'goods4',24:'goods5',25:'goods5',26:'goods5',36:'goods6',37:'goods6',38:'goods6',42:'goods7',43:'goods7',44:'goods7',48:'goods8',49:'goods8',50:'goods8',54:'goods9',55:'goods9',56:'goods9',60:'goods10',61:'goods10',62:'goods10',66:'goods11',67:'goods11',68:'goods11',72:'goods12',73:'goods12',74:'goods12',78:'goods13',79:'goods13',80:'goods13',84:'goods14',85:'goods14',86:'goods14',90:'goods15',91:'goods15',92:'goods15',96:'goods16',97:'goods16',98:'goods16',102:'goods17',103:'goods17',104:'goods17',108:'goods18',109:'goods18',110:'goods18',114:'goods19',115:'goods19',116:'goods19',30:'goods20',31:'goods20',32:'goods20',120:'goods20',121:'goods20',122:'goods20',126:'goods14',127:'goods14',128:'goods14',132:'goods15',133:'goods15',134:'goods15',138:'goods16',139:'goods16',140:'goods16',144:'goods17',145:'goods17',146:'goods17',150:'goods18',151:'goods18',152:'goods18',},  #开服第n天有效
  'goods1':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[40,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item055',1],
      'price':[50,500,30],
      'limit':10,
    },
    '3':{
      'reward':['item222',5],
      'price':[50,250,50],
      'limit':15,
    },
    '4':{
      'reward':['item217',5],
      'price':[150,500,50],
      'limit':15,
    },
    '5':{
      'reward':['item236',5],
      'price':[200,750,100],
      'limit':15,
    },
    '6':{
      'reward':['item233',5],
      'price':[200,750,100],
      'limit':10,
    },
    '7':{
      'reward':['item237',5],
      'price':[200,750,100],
      'limit':10,
    },
    '8':{
      'reward':['item709',5],
      'price':[200,4000,200],
      'limit':5,
    },
  },
  'goods2':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item059',3],
      'price':[20,200,6],
      'limit':20,
    },
    '3':{
      'reward':['item055',1],
      'price':[50,500,30],
      'limit':10,
    },
    '4':{
      'reward':['item223',5],
      'price':[100,500,30],
      'limit':15,
    },
    '5':{
      'reward':['item229',5],
      'price':[100,500,100],
      'limit':15,
    },
    '6':{
      'reward':['item233',5],
      'price':[150,750,100],
      'limit':15,
    },
    '7':{
      'reward':['item238',5],
      'price':[150,750,100],
      'limit':15,
    },
    '8':{
      'reward':['item714',5],
      'price':[250,5000,200],
      'limit':10,
    },
  },
  'goods3':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item059',3],
      'price':[20,200,6],
      'limit':20,
    },
    '3':{
      'reward':['item267',5],
      'price':[100,500,30],
      'limit':15,
    },
    '4':{
      'reward':['item266',5],
      'price':[100,500,30],
      'limit':15,
    },
    '5':{
      'reward':['item228',5],
      'price':[100,500,100],
      'limit':15,
    },
    '6':{
      'reward':['item212',5],
      'price':[150,750,100],
      'limit':15,
    },
    '7':{
      'reward':['item233',5],
      'price':[150,750,100],
      'limit':15,
    },
    '8':{
      'reward':['item707',5],
      'price':[250,5000,200],
      'limit':10,
    },
  },
  'goods4':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item260',5],
      'price':[150,500,30],
      'limit':20,
    },
    '3':{
      'reward':['item263',5],
      'price':[150,500,100],
      'limit':20,
    },
    '4':{
      'reward':['item229',5],
      'price':[100,500,100],
      'limit':20,
    },
    '5':{
      'reward':['item233',5],
      'price':[150,750,100],
      'limit':20,
    },
    '6':{
      'reward':['item238',5],
      'price':[150,750,150],
      'limit':20,
    },
    '7':{
      'reward':['item714',5],
      'price':[250,5000,250],
      'limit':15,
    },
    '8':{
      'reward':['item413',5],
      'price':[250,5000,300],
      'limit':120,
    },
  },
  'goods5':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item076',1],
      'price':[50,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item260',5],
      'price':[150,500,30],
      'limit':15,
    },
    '3':{
      'reward':['item714',5],
      'price':[250,5000,50],
      'limit':15,
    },
    '4':{
      'reward':['item413',5],
      'price':[250,5000,100],
      'limit':40,
    },
    '5':{
      'reward':['item414',5],
      'price':[250,5000,200],
      'limit':40,
    },
    '6':{
      'reward':['item415',5],
      'price':[250,5000,300],
      'limit':40,
    },
    '7':{
      'reward':['item418',5],
      'price':[250,5000,600],
      'limit':40,
    },
    '8':{
      'reward':['item421',5],
      'price':[400,8000,1000],
      'limit':40,
    },
  },
  'goods6':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item056',1],
      'price':[50,500,30],
      'limit':10,
    },
    '3':{
      'reward':['item201',5],
      'price':[20,100,30],
      'limit':50,
    },
    '4':{
      'reward':['item228',5],
      'price':[100,500,50],
      'limit':20,
    },
    '5':{
      'reward':['item427',5],
      'price':[200,2000,50],
      'limit':20,
    },
    '6':{
      'reward':['item237',5],
      'price':[150,750,100],
      'limit':10,
    },
    '7':{
      'reward':['item271',5],
      'price':[200,1500,100],
      'limit':15,
    },
    '8':{
      'reward':['item718',5],
      'price':[250,5000,200],
      'limit':10,
    },
  },
  'goods7':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':30,
    },
    '2':{
      'reward':['item056',1],
      'price':[50,500,30],
      'limit':15,
    },
    '3':{
      'reward':['item201',5],
      'price':[20,100,30],
      'limit':50,
    },
    '4':{
      'reward':['item228',5],
      'price':[100,500,50],
      'limit':20,
    },
    '5':{
      'reward':['item235',5],
      'price':[150,750,100],
      'limit':20,
    },
    '6':{
      'reward':['item237',5],
      'price':[150,750,100],
      'limit':20,
    },
    '7':{
      'reward':['item271',5],
      'price':[200,1500,100],
      'limit':15,
    },
    '8':{
      'reward':['item718',5],
      'price':[250,5000,200],
      'limit':10,
    },
  },
  'goods8':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item076',1],
      'price':[50,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item260',5],
      'price':[150,500,30],
      'limit':15,
    },
    '3':{
      'reward':['item235',5],
      'price':[150,750,100],
      'limit':20,
    },
    '4':{
      'reward':['item233',5],
      'price':[150,750,100],
      'limit':20,
    },
    '5':{
      'reward':['item238',5],
      'price':[150,750,100],
      'limit':20,
    },
    '6':{
      'reward':['item237',5],
      'price':[150,750,100],
      'limit':20,
    },
    '7':{
      'reward':['item718',5],
      'price':[250,5000,200],
      'limit':10,
    },
    '8':{
      'reward':['item720',5],
      'price':[250,5000,200],
      'limit':10,
    },
  },
  'goods9':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item222',5],
      'price':[100,250,30],
      'limit':20,
    },
    '3':{
      'reward':['item208',5],
      'price':[100,250,30],
      'limit':20,
    },
    '4':{
      'reward':['item221',5],
      'price':[100,250,50],
      'limit':15,
    },
    '5':{
      'reward':['item290',5],
      'price':[150,750,150],
      'limit':15,
    },
    '6':{
      'reward':['item223',5],
      'price':[150,750,150],
      'limit':15,
    },
    '7':{
      'reward':['item212',5],
      'price':[150,750,150],
      'limit':15,
    },
    '8':{
      'reward':['item271',5],
      'price':[200,1500,200],
      'limit':15,
    },
  },
  'goods10':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item056',5],
      'price':[50,500,30],
      'limit':100,
    },
    '3':{
      'reward':['item406',25],
      'price':[50,125,30],
      'limit':40,
    },
    '4':{
      'reward':['item409',25],
      'price':[50,125,30],
      'limit':40,
    },
    '5':{
      'reward':['item407',25],
      'price':[50,125,30],
      'limit':40,
    },
    '6':{
      'reward':['item408',25],
      'price':[50,125,30],
      'limit':40,
    },
    '7':{
      'reward':['item410',25],
      'price':[100,250,100],
      'limit':40,
    },
    '8':{
      'reward':['item084',1],
      'price':[1000,10000,500],
      'limit':10,
    },
  },
  'goods11':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item225',5],
      'price':[80,500,30],
      'limit':20,
    },
    '3':{
      'reward':['item226',5],
      'price':[80,500,30],
      'limit':20,
    },
    '4':{
      'reward':['item236',5],
      'price':[150,750,100],
      'limit':20,
    },
    '5':{
      'reward':['item237',5],
      'price':[150,750,100],
      'limit':20,
    },
    '6':{
      'reward':['item212',5],
      'price':[150,750,100],
      'limit':15,
    },
    '7':{
      'reward':['item602',2],
      'price':[200,2000,200],
      'limit':10,
    },
    '8':{
      'reward':['item605',4],
      'price':[200,2000,200],
      'limit':10,
    },
  },
  'goods12':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item034',200],
      'price':[50,400,6],
      'limit':50,
    },
    '3':{
      'reward':['item235',5],
      'price':[150,750,100],
      'limit':20,
    },
    '4':{
      'reward':['item233',5],
      'price':[150,750,100],
      'limit':15,
    },
    '5':{
      'reward':['item238',5],
      'price':[150,750,100],
      'limit':15,
    },
    '6':{
      'reward':['item602',2],
      'price':[200,2000,200],
      'limit':10,
    },
    '7':{
      'reward':['item605',4],
      'price':[200,2000,200],
      'limit':10,
    },
    '8':{
      'reward':['item035',1],
      'price':[1000,10000,300],
      'limit':5,
    },
  },
  'goods13':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item406',50],
      'price':[100,250,6],
      'limit':40,
    },
    '2':{
      'reward':['item409',50],
      'price':[100,250,6],
      'limit':40,
    },
    '3':{
      'reward':['item407',50],
      'price':[100,250,6],
      'limit':40,
    },
    '4':{
      'reward':['item408',50],
      'price':[100,250,6],
      'limit':40,
    },
    '5':{
      'reward':['item410',50],
      'price':[100,250,30],
      'limit':40,
    },
    '6':{
      'reward':['item602',2],
      'price':[200,2000,200],
      'limit':10,
    },
    '7':{
      'reward':['item605',4],
      'price':[200,2000,200],
      'limit':10,
    },
    '8':{
      'reward':['item084',1],
      'price':[1000,10000,500],
      'limit':10,
    },
  },
  'goods14':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item030',3],
      'price':[100,150,30],
      'limit':20,
    },
    '3':{
      'reward':['item034',200],
      'price':[50,400,6],
      'limit':50,
    },
    '4':{
      'reward':['item227',5],
      'price':[80,500,30],
      'limit':20,
    },
    '5':{
      'reward':['item226',5],
      'price':[80,500,30],
      'limit':20,
    },
    '6':{
      'reward':['item272',5],
      'price':[200,1500,200],
      'limit':15,
    },
    '7':{
      'reward':['item241',5],
      'price':[200,1500,200],
      'limit':15,
    },
    '8':{
      'reward':['item035',1],
      'price':[1000,10000,300],
      'limit':5,
    },
  },
  'goods15':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item030',3],
      'price':[100,150,30],
      'limit':20,
    },
    '3':{
      'reward':['item034',200],
      'price':[50,400,6],
      'limit':50,
    },
    '4':{
      'reward':['item261',5],
      'price':[100,500,30],
      'limit':15,
    },
    '5':{
      'reward':['item270',5],
      'price':[100,500,30],
      'limit':15,
    },
    '6':{
      'reward':['item260',5],
      'price':[100,500,100],
      'limit':15,
    },
    '7':{
      'reward':['item271',5],
      'price':[200,1500,100],
      'limit':15,
    },
    '8':{
      'reward':['item084',1],
      'price':[1000,10000,500],
      'limit':10,
    },
  },
  'goods16':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item030',3],
      'price':[100,150,30],
      'limit':20,
    },
    '3':{
      'reward':['item238',5],
      'price':[150,750,50],
      'limit':20,
    },
    '4':{
      'reward':['item237',5],
      'price':[150,750,50],
      'limit':20,
    },
    '5':{
      'reward':['item271',5],
      'price':[200,1500,100],
      'limit':15,
    },
    '6':{
      'reward':['item272',5],
      'price':[200,1500,100],
      'limit':15,
    },
    '7':{
      'reward':['item241',5],
      'price':[200,1500,100],
      'limit':15,
    },
    '8':{
      'reward':['item035',1],
      'price':[1000,10000,300],
      'limit':5,
    },
  },
  'goods17':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item034',200],
      'price':[50,400,6],
      'limit':50,
    },
    '3':{
      'reward':['item030',3],
      'price':[100,150,30],
      'limit':20,
    },
    '4':{
      'reward':['item605',4],
      'price':[200,2000,100],
      'limit':10,
    },
    '5':{
      'reward':['item707',4],
      'price':[200,2000,200],
      'limit':10,
    },
    '6':{
      'reward':['item714',5],
      'price':[250,5000,200],
      'limit':10,
    },
    '7':{
      'reward':['item273',5],
      'price':[200,1500,100],
      'limit':15,
    },
    '8':{
      'reward':['item035',1],
      'price':[1000,10000,300],
      'limit':5,
    },
  },
  'goods18':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item034',200],
      'price':[50,400,6],
      'limit':50,
    },
    '3':{
      'reward':['item030',3],
      'price':[100,150,30],
      'limit':20,
    },
    '4':{
      'reward':['item605',4],
      'price':[200,2000,100],
      'limit':10,
    },
    '5':{
      'reward':['item718',5],
      'price':[250,5000,200],
      'limit':10,
    },
    '6':{
      'reward':['item720',5],
      'price':[250,5000,200],
      'limit':10,
    },
    '7':{
      'reward':['item242',5],
      'price':[200,1500,100],
      'limit':15,
    },
    '8':{
      'reward':['item035',1],
      'price':[1000,10000,300],
      'limit':5,
    },
  },
  'goods19':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item034',200],
      'price':[50,400,6],
      'limit':50,
    },
    '3':{
      'reward':['item030',3],
      'price':[100,150,30],
      'limit':20,
    },
    '4':{
      'reward':['item605',4],
      'price':[200,2000,100],
      'limit':10,
    },
    '5':{
      'reward':['item263',5],
      'price':[150,500,150],
      'limit':15,
    },
    '6':{
      'reward':['item264',5],
      'price':[150,500,150],
      'limit':15,
    },
    '7':{
      'reward':['item262',5],
      'price':[150,500,150],
      'limit':15,
    },
    '8':{
      'reward':['item265',5],
      'price':[150,500,150],
      'limit':15,
    },
  },
  'goods20':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':40,
    },
    '2':{
      'reward':['item058',5],
      'price':[150,1500,30],
      'limit':15,
    },
    '3':{
      'reward':['item209',5],
      'price':[20,100,30],
      'limit':50,
    },
    '4':{
      'reward':['item208',5],
      'price':[20,100,30],
      'limit':50,
    },
    '5':{
      'reward':['item262',5],
      'price':[150,500,100],
      'limit':15,
    },
    '6':{
      'reward':['item243',5],
      'price':[200,1500,100],
      'limit':30,
    },
    '7':{
      'reward':['item770',5],
      'price':[200,4000,150],
      'limit':20,
    },
    '8':{
      'reward':['item711',5],
      'price':[250,5000,300],
      'limit':30,
    },
  },
},















############限时免单########################
'limit_free':{    #开服后第N天开启，当天可见（同一天只会有一个）
  'begin_time':[[12,0,14,0],[18,0,22,0]],  #开启后持续过当天
  'open_days':{4:'goods1',5:'goods1',6:'goods1',10:'goods2',11:'goods2',12:'goods2',16:'goods3',17:'goods3',18:'goods3',27:'goods4',28:'goods4',29:'goods4',33:'goods5',34:'goods5',35:'goods5',45:'goods6',46:'goods6',47:'goods6',51:'goods7',52:'goods7',53:'goods7',57:'goods8',58:'goods8',59:'goods8',63:'goods9',64:'goods9',65:'goods9',69:'goods10',70:'goods10',71:'goods10',75:'goods11',76:'goods11',77:'goods11',81:'goods12',82:'goods12',83:'goods12',87:'goods13',88:'goods13',89:'goods13',93:'goods14',94:'goods14',95:'goods14',99:'goods15',100:'goods15',101:'goods15',21:'goods16',22:'goods16',23:'goods16',105:'goods17',106:'goods17',107:'goods17',111:'goods18',112:'goods18',113:'goods18',117:'goods19',118:'goods19',119:'goods19',39:'goods20',40:'goods20',41:'goods20',123:'goods20',124:'goods20',125:'goods20',129:'goods13',130:'goods13',131:'goods13',135:'goods14',136:'goods14',137:'goods14',141:'goods21',142:'goods21',143:'goods21',147:'goods22',148:'goods22',149:'goods22',153:'goods23',154:'goods23',155:'goods23',},  #开服第n天有效
  'buy_limit':[100,150,200,300,300],  #数组长度为可够买次数，每次购买all_limit次
  'all_limit':30,   #可购买道具的总次数上限，也是购买一次增加的次数
  'free_show':'free_show_speak',
  'goods1':{ 
    'free_chance':0.4, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item412':5},
        'price':250,
        'limit':120,
      },
      '2':{
        'reward':{'item216':5},
        'price':200,
        'limit':25,
      },
      '3':{
        'reward':{'item037':5},
        'price':150,
       'limit':25,
      },
      '4':{
        'reward':{'item230':5},
        'price':250,
        'limit':25,
      },
      '5':{
        'reward':{'item235':5},
        'price':250,
        'limit':25,
      },
      '6':{
        'reward':{'item723':5},
        'price':250,
        'limit':20,
      },
    },
  },
  'goods2':{ 
    'free_chance':0.4, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item037':5},
        'price':150,
        'limit':25,
      },
      '2':{
        'reward':{'item216':5},
        'price':200,
        'limit':25,
      },
      '3':{
        'reward':{'item236':5},
        'price':250,
       'limit':25,
      },
      '4':{
        'reward':{'item234':5},
        'price':250,
        'limit':25,
      },
      '5':{
        'reward':{'item703':5},
        'price':200,
        'limit':25,
      },
      '6':{
        'reward':{'item713':5},
        'price':250,
        'limit':30,
      },
    },
  },
  'goods3':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item212':5},
        'price':250,
        'limit':25,
      },
      '2':{
        'reward':{'item235':5},
        'price':250,
        'limit':25,
      },
      '3':{
        'reward':{'item234':5},
        'price':250,
       'limit':25,
      },
      '4':{
        'reward':{'item723':5},
        'price':250,
        'limit':30,
      },
      '5':{
        'reward':{'item713':5},
        'price':250,
        'limit':30,
      },
      '6':{
        'reward':{'item761':5},
        'price':250,
        'limit':30,
      },
    },
  },
  'goods4':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item427':5},
        'price':250,
        'limit':80,
      },
      '3':{
        'reward':{'item709':5},
        'price':250,
       'limit':30,
      },
      '4':{
        'reward':{'item707':5},
        'price':250,
        'limit':30,
      },
      '5':{
        'reward':{'item723':5},
        'price':250,
        'limit':30,
      },
      '6':{
        'reward':{'item713':5},
        'price':250,
        'limit':30,
      },
    },
  },
  'goods5':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item426':5},
        'price':300,
        'limit':120,
      },
      '2':{
        'reward':{'item406':25},
        'price':100,
        'limit':100,
      },
      '3':{
        'reward':{'item409':25},
        'price':100,
       'limit':100,
      },
      '4':{
        'reward':{'item407':25},
        'price':100,
        'limit':100,
      },
      '5':{
        'reward':{'item408':25},
        'price':100,
        'limit':100,
      },
      '6':{
        'reward':{'item410':25},
        'price':200,
        'limit':100,
      },
    },
  },
  'goods6':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item261':5},
        'price':250,
        'limit':25,
      },
      '2':{
        'reward':{'item271':5},
        'price':300,
        'limit':25,
      },
      '3':{
        'reward':{'item235':5},
        'price':250,
       'limit':25,
      },
      '4':{
        'reward':{'item236':5},
        'price':250,
        'limit':25,
      },
      '5':{
        'reward':{'item234':5},
        'price':250,
        'limit':25,
      },
      '6':{
        'reward':{'item238':5},
        'price':250,
        'limit':25,
      },
    },
  },
  'goods7':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item260':5},
        'price':250,
        'limit':25,
      },
      '2':{
        'reward':{'item270':5},
        'price':250,
        'limit':25,
      },
      '3':{
        'reward':{'item262':5},
        'price':250,
       'limit':25,
      },
      '4':{
        'reward':{'item263':5},
        'price':250,
        'limit':25,
      },
      '5':{
        'reward':{'item264':5},
        'price':250,
        'limit':25,
      },
      '6':{
        'reward':{'item265':5},
        'price':250,
        'limit':25,
      },
    },
  },
  'goods8':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item212':5},
        'price':200,
        'limit':25,
      },
      '2':{
        'reward':{'item290':5},
        'price':200,
        'limit':25,
      },
      '3':{
        'reward':{'item233':5},
        'price':200,
       'limit':25,
      },
      '4':{
        'reward':{'item235':5},
        'price':250,
        'limit':25,
      },
      '5':{
        'reward':{'item238':5},
        'price':250,
        'limit':25,
      },
      '6':{
        'reward':{'item723':5},
        'price':200,
        'limit':20,
      },
    },
  },
  'goods9':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item201':5},
        'price':60,
        'limit':30,
      },
      '2':{
        'reward':{'item289':5},
        'price':60,
        'limit':30,
      },
      '3':{
        'reward':{'item202':5},
        'price':120,
       'limit':30,
      },
      '4':{
        'reward':{'item204':5},
        'price':120,
        'limit':30,
      },
      '5':{
        'reward':{'item203':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item205':5},
        'price':200,
        'limit':30,
      },
    },
  },
  'goods10':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item207':5},
        'price':60,
        'limit':30,
      },
      '2':{
        'reward':{'item209':5},
        'price':60,
        'limit':30,
      },
      '3':{
        'reward':{'item208':5},
        'price':120,
       'limit':30,
      },
      '4':{
        'reward':{'item210':5},
        'price':120,
        'limit':30,
      },
      '5':{
        'reward':{'item290':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item212':5},
        'price':200,
        'limit':30,
      },
    },
  },
  'goods11':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item213':5},
        'price':60,
        'limit':30,
      },
      '2':{
        'reward':{'item215':5},
        'price':60,
        'limit':30,
      },
      '3':{
        'reward':{'item214':5},
        'price':120,
       'limit':30,
      },
      '4':{
        'reward':{'item216':5},
        'price':120,
        'limit':30,
      },
      '5':{
        'reward':{'item217':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item218':5},
        'price':200,
        'limit':30,
      },
    },
  },
  'goods12':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item219':5},
        'price':60,
        'limit':30,
      },
      '2':{
        'reward':{'item220':5},
        'price':60,
        'limit':30,
      },
      '3':{
        'reward':{'item221':5},
        'price':120,
       'limit':30,
      },
      '4':{
        'reward':{'item222':5},
        'price':120,
        'limit':30,
      },
      '5':{
        'reward':{'item223':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item224':5},
        'price':200,
        'limit':30,
      },
    },
  },
  'goods13':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item058':3},
        'price':120,
        'limit':20,
      },
      '3':{
        'reward':{'item056':3},
        'price':200,
       'limit':20,
      },
      '4':{
        'reward':{'item723':5},
        'price':200,
        'limit':30,
      },
      '5':{
        'reward':{'item713':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item707':5},
        'price':200,
        'limit':30,
      },
    },
  },
  'goods14':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item058':3},
        'price':120,
        'limit':20,
      },
      '3':{
        'reward':{'item056':3},
        'price':200,
       'limit':20,
      },
      '4':{
        'reward':{'item761':5},
        'price':150,
        'limit':30,
      },
      '5':{
        'reward':{'item709':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item238':5},
        'price':300,
        'limit':30,
      },
    },
  },
  'goods15':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item141':5},
        'price':90,
        'limit':20,
      },
      '3':{
        'reward':{'item058':3},
        'price':120,
       'limit':20,
      },
      '4':{
        'reward':{'item234':5},
        'price':200,
        'limit':25,
      },
      '5':{
        'reward':{'item236':5},
        'price':200,
        'limit':25,
      },
      '6':{
        'reward':{'item235':5},
        'price':200,
        'limit':25,
      },
    },
  },
  'goods16':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item770':5},
        'price':250,
        'limit':30,
      },
      '3':{
        'reward':{'item201':5},
        'price':80,
       'limit':30,
      },
      '4':{
        'reward':{'item207':5},
        'price':80,
        'limit':30,
      },
      '5':{
        'reward':{'item213':5},
        'price':80,
        'limit':30,
      },
      '6':{
        'reward':{'item219':5},
        'price':80,
        'limit':30,
      },
    },
  },
  'goods17':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item058':3},
        'price':120,
        'limit':20,
      },
      '3':{
        'reward':{'item203':5},
        'price':200,
       'limit':30,
      },
      '4':{
        'reward':{'item290':5},
        'price':200,
        'limit':30,
      },
      '5':{
        'reward':{'item217':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item223':5},
        'price':200,
        'limit':30,
      },
    },
  },
  'goods18':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item056':3},
        'price':200,
        'limit':20,
      },
      '3':{
        'reward':{'item203':5},
        'price':200,
       'limit':30,
      },
      '4':{
        'reward':{'item205':5},
        'price':200,
        'limit':30,
      },
      '5':{
        'reward':{'item290':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item212':5},
        'price':200,
        'limit':30,
      },
    },
  },
  'goods19':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item058':3},
        'price':120,
        'limit':20,
      },
      '3':{
        'reward':{'item217':5},
        'price':200,
       'limit':30,
      },
      '4':{
        'reward':{'item218':5},
        'price':200,
        'limit':30,
      },
      '5':{
        'reward':{'item223':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item224':5},
        'price':200,
        'limit':30,
      },
    },
  },
  'goods20':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item058':3},
        'price':120,
        'limit':20,
      },
      '3':{
        'reward':{'item208':5},
        'price':120,
       'limit':30,
      },
      '4':{
        'reward':{'item212':5},
        'price':200,
        'limit':25,
      },
      '5':{
        'reward':{'item243':5},
        'price':250,
        'limit':30,
      },
      '6':{
        'reward':{'item711':5},
        'price':250,
        'limit':20,
      },
    },
  },
  'goods21':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item058':3},
        'price':120,
        'limit':20,
      },
      '3':{
        'reward':{'item027':5},
        'price':150,
       'limit':30,
      },
      '4':{
        'reward':{'item205':5},
        'price':200,
        'limit':30,
      },
      '5':{
        'reward':{'item218':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item243':5},
        'price':250,
        'limit':30,
      },
    },
  },
  'goods22':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item058':3},
        'price':120,
        'limit':20,
      },
      '3':{
        'reward':{'item027':5},
        'price':150,
       'limit':30,
      },
      '4':{
        'reward':{'item212':5},
        'price':200,
        'limit':30,
      },
      '5':{
        'reward':{'item224':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item243':5},
        'price':250,
        'limit':30,
      },
    },
  },
  'goods23':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item021':5},
        'price':250,
        'limit':30,
      },
      '3':{
        'reward':{'item205':5},
        'price':200,
       'limit':30,
      },
      '4':{
        'reward':{'item212':5},
        'price':200,
        'limit':30,
      },
      '5':{
        'reward':{'item217':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item224':5},
        'price':200,
        'limit':30,
      },
    },
  },
},















#################################七日嘉年华#################################
	'happy_buy':{		#开服活动，服务器开启后生效，活动结束后，停止对所有目标的计数，未领取的奖励保留，没有任何可领取的奖励，且活动时间已过期时，该功能入口消失
		#'name':'502015',		#入口按键名称
		'login':{		#登录（活动时间内，每登录一天可领取一份奖励）（奖励领取顺序按玩家登陆天数计算）
			'title':'502016',		#标题
			'icon':'icon_shop15',		#标签按键
			'reward':[
				[['item731',10],['food',100000],['item002',5]],		#金沙漏10，银币10W，中经验书5
				[['item719',10],['item034',2000],['item037',30]],		#甄姬10，万能片2Q，锦囊30
				[['coin',500],['gold',100000],['item079',30]],		#黄金500，拜帖2，骑兵囊30
				[['item719',30],['item036',2],['item080',30]],		#甄姬30，辎重箱2，步兵囊30
				[['item033',1],['item055',20],['item082',30]],		#大酒杯1，号角2，弓兵囊30
				[['item719',40],['item059',20],['item081',30]],		#幕府箱1Q，英雄囊2，方士囊30
				[['item719',60],['item075',10],['item107',30],],		#甄姬100，辅助囊2，星晷10
			],
		},
		
		#试炼功能检测的小目标	
		#building001：官邸等级
		#credit：获得战功
		#finish_gtask：完成政务次数
		#hero_star：拥有X个Y星英雄
		#pve：满星沙盘的个数
		#training：训练X个Y级士兵
		#decompose：问道次数
		#estate：产业主动行为次数
		#hero_rarity：拥有X个rarity为y个英雄
		#skill_lv：玩家拥有的英雄中，任意x个技能到达y级
		#army_lv：玩家拥有的英雄中，任意X个英雄的任意前军或后军到达y级
		#equip_lv：拥有X个lv为y的宝物
		#shogun_levelup：升级幕府的次数/幕府总等级
		#use_gold：消耗银两
		#use_coin：消耗黄金
	
		'sparta':{		#试炼（每完成一个试炼任务加1分，1分兑换一个大奖次数，计数结束后统一兑换成大奖等待抽取）
			'title':'502017',		#标题
			'info':'502052',		#大将说明
			'icon':'icon_shop16',		#标签按键
			'big_reward':'random953',	#大奖奖池
			'big_reward_num':100,		#大奖数量上限
			'task':{		#任务目标
				'building_lvup':{		#官邸等级
					'title':'502018',		#标题
					'info':'502019',		#说明
					'hero':'hero761',		#立绘
					'index':'11',		#标签入口，第一天第一个
					'target':[		#计数目标
						[[7],{'item141':10}],		#国士
						[[8],{'item141':10}],
						[[9],{'item141':10}],
						[[10],{'item087':1}],
						[[12],{'item087':1}],
						[[14],{'item087':1}],
						[[16],{'item087':1}],
					]
				},
				'credit':{		#战功
					'title':'502020',		#标题
					'info':'502021',		#说明
					'hero':'hero719',		#立绘
					'index':'12',
					'target':[		#计数目标
						[[100],{'merit':100}],
						[[5000],{'merit':200}],
						[[8000],{'merit':300}],
						[[10000],{'merit':400}],
						[[30000],{'merit':500}],
						[[50000],{'merit':500}],
						[[100000],{'merit':1000}],
					]
				},
				'finish_gtask':{		#政务次数
					'title':'502022',		#标题
					'info':'502023',		#说明
					'hero':'hero709',		#立绘
					'index':'13',		#标签入口
					'target':[		#计数目标
						[[3],{'item107':10}],		#英雄技·壹
						[[5],{'item107':10}],
						[[10],{'item107':10}],
						[[15],{'item088':1}],
						[[20],{'item088':1}],
						[[30],{'item088':1}],
						[[40],{'item088':1}],
						
					]
				},
				'hero_star':{		#拥有X个Y星英雄（xy的判定都是大于等于）
					'title':'502024',		#标题
					'info':'502025',		#说明
					'hero':'hero726',		#立绘
					'index':'21',		#标签入口
					'target':[		#计数目标
						[[1,6],{'item032':10}],
						[[2,6],{'item032':20}],
						[[3,6],{'item032':30}],
						[[1,12],{'item033':1}],
						[[2,12],{'item033':2}],
					]
				},
				'pve':{		#通关X个满星沙盘关卡
					'title':'502026',		#标题
					'info':'502027',		#说明
					'hero':'hero760',		#立绘
					'index':'22',		#标签入口
					'target':[		#计数目标
						[[8],{'item034':1000}],
						[[20],{'item034':2000}],
						[[28],{'item034':2000}],
						[[36],{'item034':3000}],
						[[40],{'item034':3000}],
						[[48],{'item034':4000}],
						[[56],{'item034':4000}],
						[[64],{'item034':5000}],
					]
				},
				'training':{		#训练X个Y级士兵（xy的判定都是大于等于）
					'title':'502028',		#标题
					'info':'502029',		#说明
					'hero':'hero712',		#立绘
					'index':'23',		#标签入口
					'target':[		#计数目标
						[[1000,2],{'food':30000}],
						[[5000,2],{'food':50000}],
						[[10000,2],{'food':70000}],
						[[1000,3],{'food':100000}],
						[[5000,3],{'food':150000}],
						[[10000,3],{'food':200000}],
						[[20000,3],{'food':300000}],
					]
				},
				'decompose':{		#问道次数
					'title':'502030',		#标题
					'info':'502031',		#说明
					'hero':'hero717',		#立绘
					'index':'31',		#标签入口
					'target':[		#计数目标
						[[1],{'item107':10}],
						[[10],{'item107':10}],
						[[30],{'item107':20}],
						[[50],{'item107':20}],
						[[100],{'item107':30}],
						[[150],{'item107':30}],
						[[200],{'item107':50}],
					]
				},
				'estate':{		#产业主动行为
					'title':'502032',		#标题
					'info':'502033',		#说明
					'hero':'hero405',		#立绘
					'index':'32',		#标签入口
					'target':[		#计数目标
						[[1],{'item070':1}],
						[[5],{'item070':2}],
						[[10],{'item070':2}],
						[[20],{'item070':3}],
						[[30],{'item070':3}],
						[[40],{'item070':5}],
					]
				},
				'hero_rarity':{		#拥有X个rarity为y个英雄（xy的判定都是大于等于）
					'title':'502034',		#标题
					'info':'502035',		#说明
					'hero':'hero725',		#立绘
					'index':'33',		#标签入口
					'target':[		#计数目标
						[[1,2],{'gold':10000}],
						[[2,2],{'gold':30000}],
						[[3,2],{'gold':50000}],
						[[4,2],{'gold':70000}],
						[[5,2],{'gold':100000}],
						[[6,2],{'gold':150000}],
					]
				},
				'skill_lv':{		#任意x个技能到达y级（xy的判定都是大于等于）
					'title':'502036',		#标题
					'info':'502037',		#说明
					'hero':'hero733',		#立绘
					'index':'41',		#标签入口
					'target':[		#计数目标
						[[1,8],{'gold':10000}],
						[[1,10],{'gold':30000}],
						[[1,13],{'gold':50000}],
						[[2,10],{'gold':70000}],
						[[2,13],{'gold':100000}],
						[[3,10],{'gold':150000}],
						[[3,13],{'gold':200000}],
						[[4,15],{'gold':300000}],
					]
				},
				'army_lv':{		#任意X个英雄的任意前军或后军到达y级（xy的判定都是大于等于）
					'title':'502038',		#标题
					'info':'502039',		#说明
					'hero':'hero731',		#立绘
					'index':'42',		#标签入口
					'target':[		#计数目标
						[[2,10],{'item021':2}],
						[[2,20],{'item021':3}],
						[[2,30],{'item021':4}],
						[[2,40],{'item021':5}],
						[[4,30],{'item021':7}],
						[[4,45],{'item021':10}],
					]
				},
				'equip_lv':{		#拥有X个lv为y的宝物（xy的判定都是大于等于）
					'title':'502040',		#标题
					'info':'502041',		#说明
					'hero':'hero706',		#立绘
					'index':'43',		#标签入口
					'target':[		#计数目标
						[[1,0],{'food':10000}],
						[[2,0],{'food':30000}],
						[[1,1],{'food':50000}],
						[[2,1],{'food':70000}],
						[[1,2],{'food':100000}],
						[[2,2],{'food':200000}],
						[[1,3],{'food':300000}],
					]
				},
				'shogun_levelup':{		#升级幕府的次数/幕府总等级
					'title':'502042',		#标题
					'info':'502043',		#说明
					'hero':'hero732',		#立绘
					'index':'51',		#标签入口
					'target':[		#计数目标
						[[1],{'item056':10}],
						[[3],{'item056':10}],
						[[5],{'item056':20}],
						[[10],{'item056':20}],
						[[15],{'item056':30}],
						[[20],{'item056':30}],
						[[30],{'item056':50}],
					]
				},
				'use_gold':{		#消耗银两
					'title':'502044',		#标题
					'info':'502045',		#说明
					'hero':'hero418',		#立绘
					'index':'52',		#标签入口
					'target':[		#计数目标
						[[500000],{'item071':1}],
						[[1000000],{'item071':1}],
						[[2000000],{'item071':1}],
						[[3000000],{'item071':1}],
						[[4000000],{'item071':1}],
						[[5000000],{'item071':1}],
					]
				},
				'use_coin':{		#消耗黄金
					'title':'502046',		#标题
					'info':'502047',		#说明
					'hero':'hero401',		#立绘
					'index':'53',		#标签入口
					'target':[		#计数目标
						[[500],{'item030':1}],
						[[1000],{'item030':2}],
						[[2000],{'item030':3}],
						[[3000],{'item030':5}],
						[[5000],{'item030':7}],
						[[10000],{'item030':10}],
					]
				}
			}
		},
		'purchase':{		#超值礼包（累充资格，购买次数每日重置）
			'title':'502048',		#标题
			'info':'502053',		#说明
			'icon':'icon_shop17',		#标签按键
			'need_pay_money':[30,68,98,128,128,196,328],		#购买此功能的资格，需要已经累计充值的rmb，单位：rmb，显示转换为黄金
			'buy_show':[1300,2200,3100,4000,4900,5800,6700],		#用作删除线显示的内容
			'buy':[188,388,588,666,888,1088,1288],		#购买此功能的消耗，单位：黄金,，显示处理为（3888+删除线，188）
			'reward':[		#单次购买的内容，庞统+神算
			{'item761':10,'item233':20},{'item761':20,'item233':30},{'item761':30,'item233':40},
			{'item761':40,'item233':50},{'item761':50,'item233':60},{'item761':60,'item233':70},
			{'item761':70,'item233':80},
			]
		},
		'addup':{		#累计充值（可以与其他累计充值同时计数）
			'title':'502049',		#标题
			'icon':'icon_shop18',		#标签按键
			'double_days':[0,1],		#3天内充值可享2倍积分
			'reward':[		#充值需求为RMB，显示转换为黄金
				[50,[['equip',['equip036']],['item021',3],['gold',50000]]],		
				[100,[['item427',40],['item021',5],['gold',100000],['iron',200000]]],		
				[200,[['equip',['equip039']],['item021',7],['gold',150000],['iron',300000]]],		
				[500,[['item427',80],['item021',10],['gold',200000],['iron',400000]]],		#献血160,镔铁160,钱100W,金沙15,锦囊200
				[1000,[['equip',['equip050']],['item021',15],['gold',250000],['iron',500000]]],		#大黄书,镔铁200,钱250W,金沙20,万能1W
				[2000,[['item427',180],['item021',20],['food',600000],['wood',600000]]],		#献血300,鞍座300,钱250W,金沙25,万能2W
				[3000,[['equip',['equip054']],['item021',25],['food',700000],['wood',700000]]],		#大帽子,鞍座300,钱250W,金沙30,万能2W
				[4000,[['item427',360],['item021',30],['gold',400000],['food',800000],]],		#献血300,草纸300,辎重50,金沙35,万能2W
				[5000,[['equip',['equip047']],['item021',35],['gold',450000],['food',900000]]],		#大卷轴,草纸300,辎重50,金沙40,万能2W
				[6000,[['item427',540],['item021',40],['wood',1000000],['iron',1000000],]],		#献血350,玄晶600,辎重50,金沙45,万能2W
				[8000,[['item427',720],['item021',45],['wood',1500000],['iron',1500000],]],		#献血450,玄晶600,辎重50,金沙50,神经中200
				[10000,[['item427',900],['item021',50],['gold',1000000],['food',2000000],]],		#献血900,璞玉600,辎重50,金沙55,星经中200
			],
			'show':['equip036','equip054','equip047','equip039','equip050'],		#展示套装中的装备显示
			'info':'502014'		#展示中的文本
		},
		'once':{		#每日单笔充值（活动期间内，每日单笔充值一次，可以领取一次奖励，累计充值达到指定天数，获得指定奖励）
			'title':'502050',		#标题
			'icon':'icon_shop19',		#标签按键
			'need_days':[[3,{'item260':40}],[5,{'item260':60}]],		#第3，5天累计奖励，鬼谋40，鬼谋60，一次性奖励
			'reward_every':{'item036':5,'item021':5,'item030':5,'item037':30},		#每日充值奖励，辎重2，金沙5，行军5，锦囊30（新奖励覆盖旧奖励）
		}
	},
###########################累计消费###########################
           'consume':{       
                   'title':'502054',
                   'msg_name': '502001',
                   'msg_info': '502002',
                   'open_days':{'1_2':'C1','3_4':'C2','5_6':'C2','7_8':'C3','9_10':'C4','11_12':'C5','13_14':'C6','15_16':'C4','17_18':'C4','19_20':'C4','21_22':'C4','23_24':'C4','25_26':'C4','27_28':'C4','29_30':'C4','31_32':'C4','33_34':'C4','35_36':'C4','37_38':'C4','39_40':'C4','41_42':'C4','43_44':'C4','45_46':'C4','47_48':'C4','49_50':'C4','51_52':'C4','53_54':'C4','55_56':'C4','57_58':'C4','59_60':'C4','61_62':'C4','63_64':'C4','65_66':'C4','67_68':'C4','69_70':'C4','71_72':'C4','73_74':'C4','75_76':'C4','77_78':'C4','79_80':'C4','81_82':'C4','83_84':'C4','85_86':'C4','87_88':'C4','89_90':'C4','91_92':'C4','93_94':'C4','95_96':'C4','97_98':'C4','99_100':'C4','101_102':'C4','103_104':'C4','105_106':'C4','107_108':'C4','109_110':'C4','111_112':'C4','113_114':'C4','115_116':'C4','117_118':'C4','119_120':'C4','121_122':'C4','123_124':'C4','125_126':'C4','127_128':'C4','129_130':'C4','131_132':'C4','133_134':'C4','135_136':'C4','137_138':'C4','139_140':'C4','141_142':'C4','143_144':'C4','145_146':'C4','147_148':'C4','149_150':'C4','151_152':'C4','153_154':'C4','155_156':'C4','157_158':'C4','159_160':'C4','161_162':'C4',},
                        
         'C1':{      #第一期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'food':30000,'item037':5,},
                                      800:{'food':50000,'item037':10,},
                                      1500:{'food':70000,'item037':15,'item101':1,},
                                      3000:{'food':100000,'item032':10,'item037':20,},
                                      5000:{'gold':75000,'food':150000,'item037':25,'item104':1,},
                                      10000:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item021':20,},
                                      20000:{'gold':150000,'food':300000,'wood':300000,'iron':300000,'item107':70,},
                                      
                                     },

                            },     
         'C2':{      #第二期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'food':50000,'item037':10,},
                                      800:{'food':70000,'item037':20,},
                                      1500:{'food':100000,'item037':30,'item101':1,},
                                      3000:{'food':150000,'item032':10,'item037':40,},
                                      5000:{'gold':100000,'food':200000,'item037':25,'item104':1,},
                                      10000:{'gold':150000,'food':300000,'wood':200000,'iron':200000,'item107':50,},
                                      20000:{'gold':200000,'food':400000,'wood':300000,'iron':300000,'item107':70,},
                                      30000:{'gold':250000,'food':500000,'wood':500000,'iron':500000,'item107':100,},
                                     },

                            },     
         'C3':{      #第三期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'food':100000,'item037':20,},
                                      800:{'food':150000,'item037':30,},
                                      1500:{'food':200000,'item037':40,'item104':1,},
                                      3000:{'food':250000,'item032':20,'item037':50,},
                                      5000:{'gold':150000,'food':300000,'item037':60,'item104':2,},
                                      10000:{'gold':200000,'food':400000,'wood':400000,'iron':400000,'item107':100,},
                                      20000:{'gold':250000,'food':500000,'wood':500000,'iron':500000,'item107':150,},
                                      30000:{'gold':300000,'food':600000,'wood':600000,'iron':600000,'item107':200,},
                                     },

                            },     
         'C4':{      #第四期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':75000,'food':50000,'item130':50,},
                                      800:{'gold':100000,'food':100000,'item113':50,},
                                      1500:{'gold':125000,'food':150000,'item107':50,},
                                      3000:{'gold':150000,'food':200000,'item130':100,},
                                      5000:{'gold':200000,'food':250000,'item113':100,},
                                      10000:{'gold':300000,'food':300000,'wood':100000,'iron':100000,'item107':100,},
                                      20000:{'gold':400000,'food':350000,'wood':200000,'iron':200000,'item113':200,},
                                      30000:{'gold':500000,'food':400000,'wood':300000,'iron':300000,'item107':300,},
                                     },

                            },     
         'C5':{      #第五期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':100000,'food':100000,'item130':50,},
                                      800:{'gold':150000,'food':150000,'item113':50,},
                                      1500:{'gold':200000,'food':200000,'item107':50,},
                                      3000:{'gold':250000,'food':250000,'item130':100,},
                                      5000:{'gold':300000,'food':300000,'item113':100,},
                                      10000:{'gold':400000,'food':350000,'wood':150000,'iron':150000,'item107':150,},
                                      20000:{'gold':500000,'food':400000,'wood':200000,'iron':200000,'item113':250,},
                                      30000:{'gold':600000,'food':450000,'wood':250000,'iron':250000,'item107':350,},
                                     },

                            },     
         'C6':{      #第六期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':150000,'food':150000,'item130':50,},
                                      800:{'gold':200000,'food':200000,'item113':100,},
                                      1500:{'gold':250000,'food':250000,'item107':100,},
                                      3000:{'gold':300000,'food':300000,'item130':150,},
                                      5000:{'gold':350000,'food':350000,'item113':150,},
                                      10000:{'gold':450000,'food':400000,'wood':200000,'iron':200000,'item107':200,},
                                      20000:{'gold':550000,'food':450000,'wood':250000,'iron':250000,'item113':300,},
                                      30000:{'gold':650000,'food':500000,'wood':300000,'iron':300000,'item107':400,},
                                     },

                            },     
         'C7':{      #第七期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':200000,'food':200000,'item130':100,},
                                      800:{'gold':250000,'food':250000,'item113':150,},
                                      1500:{'gold':300000,'food':300000,'item107':150,},
                                      3000:{'gold':350000,'food':350000,'item130':200,},
                                      5000:{'gold':400000,'food':400000,'item113':200,},
                                      10000:{'gold':500000,'food':450000,'wood':250000,'iron':250000,'item107':250,},
                                      20000:{'gold':600000,'food':500000,'wood':300000,'iron':300000,'item113':350,},
                                      30000:{'gold':700000,'food':550000,'wood':350000,'iron':350000,'item107':450,},
                                     },

                            },     
         'C8':{      #第八期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':250000,'food':250000,'item130':150,},
                                      800:{'gold':300000,'food':300000,'item113':200,},
                                      1500:{'gold':350000,'food':350000,'item107':200,},
                                      3000:{'gold':400000,'food':400000,'item130':250,},
                                      5000:{'gold':450000,'food':450000,'item113':250,},
                                      10000:{'gold':550000,'food':500000,'wood':300000,'iron':300000,'item107':300,},
                                      20000:{'gold':650000,'food':550000,'wood':350000,'iron':350000,'item113':400,},
                                      30000:{'gold':750000,'food':600000,'wood':400000,'iron':400000,'item107':500,},
                                     },

                            },     
         'C9':{      #第九期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':300000,'food':300000,'item130':200,},
                                      800:{'gold':350000,'food':350000,'item113':250,},
                                      1500:{'gold':400000,'food':400000,'item107':250,},
                                      3000:{'gold':450000,'food':450000,'item130':300,},
                                      5000:{'gold':500000,'food':500000,'item113':300,},
                                      10000:{'gold':600000,'food':550000,'wood':350000,'iron':350000,'item107':350,},
                                      20000:{'gold':700000,'food':600000,'wood':400000,'iron':400000,'item113':450,},
                                      30000:{'gold':800000,'food':650000,'wood':450000,'iron':450000,'item107':550,},
                                     },

                            },     
         'C10':{      #第十期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':350000,'food':350000,'item130':250,},
                                      800:{'gold':400000,'food':400000,'item113':300,},
                                      1500:{'gold':450000,'food':450000,'item107':300,},
                                      3000:{'gold':500000,'food':500000,'item130':350,},
                                      5000:{'gold':550000,'food':550000,'item113':350,},
                                      10000:{'gold':650000,'food':600000,'wood':400000,'iron':400000,'item107':400,},
                                      20000:{'gold':750000,'food':650000,'wood':450000,'iron':450000,'item113':500,},
                                      30000:{'gold':850000,'food':700000,'wood':500000,'iron':500000,'item107':600,},
                                     },

                            },     
         'C11':{      #第十一期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':400000,'food':400000,'item130':300,},
                                      800:{'gold':450000,'food':450000,'item113':350,},
                                      1500:{'gold':500000,'food':500000,'item107':350,},
                                      3000:{'gold':550000,'food':550000,'item130':400,},
                                      5000:{'gold':600000,'food':600000,'item113':400,},
                                      10000:{'gold':700000,'food':650000,'wood':450000,'iron':450000,'item107':450,},
                                      20000:{'gold':800000,'food':700000,'wood':500000,'iron':500000,'item113':550,},
                                      30000:{'gold':900000,'food':750000,'wood':550000,'iron':550000,'item107':650,},
                                     },

                            },     
         'C12':{      #第十二期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':450000,'food':450000,'item130':350,},
                                      800:{'gold':500000,'food':500000,'item113':400,},
                                      1500:{'gold':550000,'food':550000,'item107':400,},
                                      3000:{'gold':600000,'food':600000,'item130':450,},
                                      5000:{'gold':650000,'food':650000,'item113':450,},
                                      10000:{'gold':750000,'food':700000,'wood':500000,'iron':500000,'item107':500,},
                                      20000:{'gold':850000,'food':750000,'wood':550000,'iron':550000,'item113':600,},
                                      30000:{'gold':950000,'food':800000,'wood':600000,'iron':600000,'item107':700,},
                                     },

                            },     
         'C13':{      #第十三期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':500000,'food':500000,'item130':400,},
                                      800:{'gold':550000,'food':550000,'item113':450,},
                                      1500:{'gold':600000,'food':600000,'item107':450,},
                                      3000:{'gold':650000,'food':650000,'item130':500,},
                                      5000:{'gold':700000,'food':700000,'item113':500,},
                                      10000:{'gold':800000,'food':750000,'wood':550000,'iron':550000,'item107':550,},
                                      20000:{'gold':900000,'food':800000,'wood':600000,'iron':600000,'item113':650,},
                                      30000:{'gold':1000000,'food':850000,'wood':650000,'iron':650000,'item107':750,},
                                     },

                            },},






#################################转盘#################################
	'dial':{		#三国秘藏（自选转盘）
		'name':'502055',		#标题名称
		'tips':'502056',		#活动说明
		'buy_num':50,		#充值满150money，可以抽奖一次
		'reward':{		#开服第几天开启，以及本次活动使用的奖池
	
			22: 'd1',
			24: 'd2',
			26: 'd1',
			28: 'd2',
			30: 'd1',
			32: 'd3',
			34: 'd3',

		},
	},
	'treasure':{		#天下珍宝
		'name':'502057',		#标题名称
		'tips':'502058',		#活动说明
		'time':24,		#活动有效期，单位，小时
		'buy_one':[1,100,1000,25],		#每期活动初始免费次数，之后每次购买消耗的黄金，以及每次获得的银币数量，每次购买获得的积分
		'buy_five':[0,488,5000,125],		#每期活动初始免费次数，之后每次购买消耗的黄金，以及每次获得的银币数量，每次购买获得的积分
		'reward':{		#开服第几天开启，以及本次活动使用的奖池
			23: 't1',
			25: 't1',
			27: 't2',
			29: 't2',
                        31: 't3',
                        33: 't3',


		},
	},
	'reward_house':{
                                                          'd1':{
'award':[
[0.6,5,[['wood',200000],['gold',100000],['food',200000],['iron',200000],['item004',5],['item034',20000],['item203',30],['item217',30],['item290',30],['item223',30],]],
[0.36,3,[['item021',10],['item030',10],['item075',10],['coin',500],['item270',25],['item260',25],['item261',25],['item071',5],['item056',50],['item058',200],]],
[0.04,2,[['star1001',1],['star0801',1],['item238',20],['item233',20],['item236',20],['item229',20],['item036',30],['item603',3],['item606',5],['item076',10],]],
],
                         #额外奖励和每个奖励所需的抽奖次数(显示处理上，如果每挡只有一个奖励则直接显示，若有多个奖励点击展开奖励内容，可领奖时都是点击直接领取

'add':{
3:{'item075':20,},
10:{'item288':100,},
15:{'item036':30,},
30:{'item035':1,},
},
},

'd2':{
'award':[
[0.6,5,[['wood',200000],['gold',100000],['food',200000],['iron',200000],['item004',5],['item034',20000],['item203',30],['item217',30],['item290',30],['item223',30],]],
[0.36,3,[['item021',10],['item030',10],['item075',10],['coin',500],['item270',25],['item260',25],['item261',25],['item071',5],['item056',50],['item058',200],]],
[0.04,2,[['star1201',1],['star1401',1],['item234',20],['item235',20],['item237',20],['item232',20],['item036',30],['item603',3],['item606',5],['item076',10],]],
],
                         #额外奖励和每个奖励所需的抽奖次数(显示处理上，如果每挡只有一个奖励则直接显示，若有多个奖励点击展开奖励内容，可领奖时都是点击直接领取

'add':{
3:{'item075':20,},
10:{'item288':100,},
15:{'item036':30,},
30:{'item035':1,},
},





},

'd3':{
'award':[
[0.6,5,[['wood',200000],['gold',100000],['food',200000],['iron',200000],['item004',5],['item034',20000],['item203',30],['item217',30],['item290',30],['item223',30],]],
[0.36,3,[['item021',10],['item030',10],['item075',10],['coin',500],['item270',25],['item260',25],['item261',25],['item071',5],['item056',50],['item058',200],]],
[0.04,2,[['star1001',1],['star0801',1],['item238',20],['item233',20],['item236',20],['item229',20],['item036',30],['item603',3],['item606',5],['item076',10],]],
],
                         #额外奖励和每个奖励所需的抽奖次数(显示处理上，如果每挡只有一个奖励则直接显示，若有多个奖励点击展开奖励内容，可领奖时都是点击直接领取

'add':{
3:{'item075':20,},
10:{'item271':100,},
15:{'item036':30,},
30:{'item035':1,},
},
},


 
                         't1':{ 
                                             'award':[
                                     [1986,'item419',10,1],
                                     [1000,'item406',10],
                                     [3000,'item030',5],
                                     [1500,'item021',5],
                                     [1666,'item205',5,1],
                                     [1666,'item212',5,1],
                                     [1500,'item037',50],
                                     [3000,'food',100000],
                           ],

                                          'add':{
                                              5:{'item603':1,},
                                              15:{'item036':15,},
                                              30:{'item606':5,},
                                              80:{'item084':3,},
                                         },


                                          'treasure_shop':[

                                      ['item419',10,100,20],
                                      ['item030',1,25,100],
                                      ['item076',1,100,50],
                                      ['item036',1,100,30],
                                      ['item262',10,100,50],
                                      ['item263',10,100,50],
                                      ['item266',10,100,50],
                                      ['item267',10,100,50],
                           ],
                         },




                         't2':{ 
                                             'award':[
                                     [1986,'item420',10,1],
                                     [1000,'item406',10],
                                     [3000,'item030',5],
                                     [1500,'item021',5],
                                     [1666,'item218',5,1],
                                     [1666,'item224',5,1],
                                     [1500,'item037',50],
                                     [3000,'food',100000],
                           ],

                                          'add':{
                                              5:{'item603':1,},
                                              15:{'item036':15,},
                                              30:{'item606':5,},
                                              80:{'item084':3,},
                                         },


                                          'treasure_shop':[

                                      ['item420',10,100,20],
                                      ['item030',1,25,100],
                                      ['item076',1,100,50],
                                      ['item036',1,100,30],
                                      ['item264',10,100,20],
                                      ['item265',10,100,20],
                                      ['item268',10,100,20],
                                      ['item269',10,100,20],
                           ],
                         },



                         't3':{ 
                                             'award':[
                                     [1145,'item770',5,1],
                                     [1000,'item406',10],
                                     [3000,'item029',5],
                                     [1500,'item021',5],
                                     [1666,'item218',5,1],
                                     [1666,'item224',5,1],
                                     [1500,'item037',50],
                                     [3000,'food',100000],
                           ],

                                          'add':{
                                              5:{'item603':1,},
                                              15:{'item036':15,},
                                              30:{'item606':5,},
                                              80:{'item084':3,},
                                         },


                                          'treasure_shop':[

                                      ['item770',5,100,20],
                                      ['item029',1,25,100],
                                      ['item076',1,100,50],
                                      ['item036',1,100,30],
                                      ['item264',10,100,20],
                                      ['item265',10,100,20],
                                      ['item268',10,100,20],
                                      ['item269',10,100,20],
                           ],
                         },



},
###########限时兑换############
        'exchange_shop':{    #兑换商店
        'name':100000,
        'sort_by':['item042','item043','item044','item045','item195'],#排序规则，使用货币道具ID
        'consume_items':['item042','item043','item044','item045','item195'],# 每个商店中消耗的货币
           'begin_time':[
 {'time':[10,11],'goods':['goods5',None,None,None,None]},
 {'time':[12,13],'goods':['goods5',None,None,None,None]},
 {'time':[14,15],'goods':['goods5','goods2',None,None,None]},
 {'time':[16,17],'goods':['goods5','goods2',None,None,'goods1']},
 {'time':[18,19],'goods':['goods1','goods2',None,None,'goods1']},
 {'time':[20,21],'goods':['goods1','goods2',None,None,'goods1']},
 {'time':[22,23],'goods':['goods1','goods2',None,None,'goods1']},
 {'time':[24,25],'goods':['goods2','goods2',None,None,'goods1']},
 {'time':[26,27],'goods':['goods2','goods2',None,None,'goods1']},
 {'time':[28,29],'goods':['goods2','goods2',None,None,'goods1']},
 {'time':[30,31],'goods':['goods6','goods2','goods2',None,'goods1']},
 {'time':[32,33],'goods':['goods3','goods2','goods2',None,'goods1']},
 {'time':[34,35],'goods':['goods7','goods2','goods2',None,'goods1']},
 {'time':[36,37],'goods':['goods7','goods2','goods2',None,'goods1']},
 {'time':[38,39],'goods':['goods7','goods2','goods2',None,'goods1']},
 {'time':[40,41],'goods':['goods8','goods2','goods2',None,'goods1']},
 {'time':[42,43],'goods':['goods8','goods2','goods2',None,'goods1']},
 {'time':[44,45],'goods':['goods8','goods2','goods2',None,'goods1']},
 {'time':[46,47],'goods':['goods8','goods2','goods2',None,'goods1']},
 {'time':[48,49],'goods':['goods8','goods2','goods2',None,'goods1']},
 {'time':[50,51],'goods':['goods8','goods2','goods2',None,'goods1']},
 {'time':[52,53],'goods':['goods8','goods2','goods2',None,'goods1']},
 {'time':[54,55],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[56,57],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[58,59],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[60,61],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[62,63],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[64,65],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[66,67],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[68,69],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[70,71],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[72,73],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[74,75],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[76,77],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[78,79],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[80,81],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[82,83],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[84,85],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[86,87],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[88,89],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[90,91],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[92,93],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[94,95],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[96,97],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[98,99],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[100,101],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[102,103],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[104,105],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[106,107],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[108,109],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[110,111],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[112,113],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[114,115],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[116,117],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[118,119],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[120,121],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[122,123],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[124,125],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[126,127],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[128,129],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[130,131],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[132,133],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[134,135],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[136,137],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[138,139],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[140,141],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[142,143],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[144,145],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[146,147],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[148,149],'goods':['goods8','goods2','goods2','goods1','goods1']},
 {'time':[150,151],'goods':['goods8','goods2','goods2','goods1','goods1']},
], #开服第几天，1=开启天数，2=结束天数 （eg：偏移值5点，1号5点开服, 'days':[1, 2] 活动入口会在3号5点关闭,[青龙qinglong_shop，白虎baihu_shop,朱雀zhuque_shop,玄武xuanwu_shop]从这四个商店里取goods，当数组里该位置为null时，商店分页显示为未开启

     'shoplists':[
        {   #五铢商店

         'goods1':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },











          ],
         'goods2':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },










          ],
         'goods3':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },

         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },








          ],
         'goods4':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },







          ],
         'goods5':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },












          ],
         'goods6':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },









          ],
         'goods7':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },







          ],
         'goods8':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item426':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },






          ],
         'goods9':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item426':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },






          ],
         'goods10':[
         {
           'reward':{'item1057':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1058':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1059':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1060':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1064':1},
           'price':600,
           'limit':10,
              },





          ],
},

{   #武帝商店

 'goods1':[
        {
           'reward':{'equip':['equip036']},
           'price':30,
           'limit':1,
              },
        {
           'reward':{'equip':['equip039']},
           'price':200,
           'limit':1,
              },
        {
           'reward':{'equip':['equip050']},
           'price':300,
           'limit':1,
              },
        {
           'reward':{'equip':['equip054']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip047']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'item427':10},
           'price':15,
           'limit':50,
              },
        {
           'reward':{'item427':200},
           'price':300,
           'limit':20,
              },
        {
           'reward':{'item602':3},
           'price':10,
           'limit':60,
              },
        {
           'reward':{'item605':6},
           'price':10,
           'limit':60,
              },






          ],
 'goods2':[
        {
           'reward':{'equip':['equip036']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip039']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip050']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip054']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip047']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item427':10},
           'price':15,
           'limit':50,
              },
        {
           'reward':{'item427':200},
           'price':300,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },







          ],
 'goods3':[
        {
           'reward':{'equip':['equip036']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip039']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip050']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip054']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip047']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item427':10},
           'price':15,
           'limit':50,
              },
        {
           'reward':{'item427':200},
           'price':300,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },







          ],
},

{   #天罚商店

 'goods1':[
        {
           'reward':{'item413':5},
           'price':8,
           'limit':120,
              },
        {
           'reward':{'item414':5},
           'price':10,
           'limit':120,
              },
        {
           'reward':{'item415':5},
           'price':12,
           'limit':120,
              },
        {
           'reward':{'item418':5},
           'price':15,
           'limit':120,
              },
        {
           'reward':{'item421':5},
           'price':20,
           'limit':120,
              },
        {
           'reward':{'item059':200},
           'price':10,
           'limit':60,
              },
        {
           'reward':{'item076':10},
           'price':10,
           'limit':60,
              },



          ],
 'goods2':[
        {
           'reward':{'item413':5},
           'price':8,
           'limit':120,
              },
        {
           'reward':{'item414':5},
           'price':10,
           'limit':120,
              },
        {
           'reward':{'item415':5},
           'price':12,
           'limit':120,
              },
        {
           'reward':{'item418':5},
           'price':15,
           'limit':120,
              },
        {
           'reward':{'item421':5},
           'price':20,
           'limit':120,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },




          ],
 'goods3':[
        {
           'reward':{'item413':5},
           'price':8,
           'limit':120,
              },
        {
           'reward':{'item414':5},
           'price':10,
           'limit':120,
              },
        {
           'reward':{'item415':5},
           'price':12,
           'limit':120,
              },
        {
           'reward':{'item418':5},
           'price':15,
           'limit':120,
              },
        {
           'reward':{'item421':5},
           'price':20,
           'limit':120,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },




          ],
},

{   #斗神商店

 'goods1':[
        {
           'reward':{'equip':['equip059']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip060']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip061']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip062']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip063']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item428':10},
           'price':20,
           'limit':50,
              },
        {
           'reward':{'item428':200},
           'price':400,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },













          ],
 'goods2':[
        {
           'reward':{'equip':['equip059']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip060']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip061']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip062']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip063']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item428':10},
           'price':20,
           'limit':1,
              },
        {
           'reward':{'item428':200},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },













          ],
 'goods3':[
        {
           'reward':{'equip':['equip059']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip060']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip061']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip062']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip063']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item428':10},
           'price':20,
           'limit':1,
              },
        {
           'reward':{'item428':200},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },













          ],
},

{   #羽林商店

 'goods1':[
        {
           'reward':{'equip':['equip070']},
           'price':20,
           'limit':1,
              },
        {
           'reward':{'equip':['equip071']},
           'price':30,
           'limit':1,
              },
        {
           'reward':{'equip':['equip072']},
           'price':40,
           'limit':1,
              },
        {
           'reward':{'equip':['equip073']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip074']},
           'price':60,
           'limit':1,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },




          ],
},

],
},
        'pay_choose':{ 
        'name':502062,
        'info':502063,
        'need_pay_coin':500,
        'limit_time':200,
        'miss_reward':['item042',10], #活动结束后，对未领取奖励的玩家进行补偿{补偿物品，数量}，邮件发奖，奖励总数=剩余可领取次数x奖励数量
        'mail_name':'502081',  #邮件标题
        'mail_info':'502082',  #邮件内容
        'begin_time':[ 
 {'time':[10,11],'goods':'goods1'},
 {'time':[12,13],'goods':'goods1'},
 {'time':[14,15],'goods':'goods7'},
 {'time':[16,17],'goods':'goods2'},
 {'time':[18,19],'goods':'goods2'},
 {'time':[20,21],'goods':'goods2'},
 {'time':[22,23],'goods':'goods2'},
 {'time':[24,25],'goods':'goods2'},
 {'time':[26,27],'goods':'goods3'},
 {'time':[28,29],'goods':'goods4'},
 {'time':[30,31],'goods':'goods8'},
 {'time':[32,33],'goods':'goods8'},
 {'time':[34,35],'goods':'goods8'},
 {'time':[36,37],'goods':'goods5'},
 {'time':[38,39],'goods':'goods5'},
 {'time':[40,41],'goods':'goods6'},
 {'time':[42,43],'goods':'goods6'},
 {'time':[44,45],'goods':'goods6'},
 {'time':[46,47],'goods':'goods6'},
 {'time':[48,49],'goods':'goods6'},
 {'time':[50,51],'goods':'goods6'},
 {'time':[52,53],'goods':'goods6'},
 {'time':[54,55],'goods':'goods6'},
 {'time':[56,57],'goods':'goods6'},
 {'time':[58,59],'goods':'goods6'},
 {'time':[60,61],'goods':'goods6'},
 {'time':[62,63],'goods':'goods6'},
 {'time':[64,65],'goods':'goods6'},
 {'time':[66,67],'goods':'goods6'},
 {'time':[68,69],'goods':'goods6'},
 {'time':[70,71],'goods':'goods6'},
 {'time':[72,73],'goods':'goods6'},
 {'time':[74,75],'goods':'goods6'},
 {'time':[76,77],'goods':'goods6'},
 {'time':[78,79],'goods':'goods6'},
 {'time':[80,81],'goods':'goods6'},
 {'time':[82,83],'goods':'goods6'},
 {'time':[84,85],'goods':'goods6'},
 {'time':[86,87],'goods':'goods6'},
 {'time':[88,89],'goods':'goods6'},
 {'time':[90,91],'goods':'goods6'},
 {'time':[92,93],'goods':'goods6'},
 {'time':[94,95],'goods':'goods6'},
 {'time':[96,97],'goods':'goods6'},
 {'time':[98,99],'goods':'goods6'},
 {'time':[100,101],'goods':'goods6'},
 {'time':[102,103],'goods':'goods6'},
 {'time':[104,105],'goods':'goods6'},
 {'time':[106,107],'goods':'goods6'},
 {'time':[108,109],'goods':'goods6'},
 {'time':[110,111],'goods':'goods6'},
 {'time':[112,113],'goods':'goods6'},
 {'time':[114,115],'goods':'goods6'},
 {'time':[116,117],'goods':'goods6'},
 {'time':[118,119],'goods':'goods6'},
 {'time':[120,121],'goods':'goods6'},
 {'time':[122,123],'goods':'goods6'},
 {'time':[124,125],'goods':'goods6'},
 {'time':[126,127],'goods':'goods6'},
 {'time':[128,129],'goods':'goods6'},
 {'time':[130,131],'goods':'goods6'},
 {'time':[132,133],'goods':'goods6'},
 {'time':[134,135],'goods':'goods6'},
 {'time':[136,137],'goods':'goods6'},
 {'time':[138,139],'goods':'goods6'},
 {'time':[140,141],'goods':'goods6'},
 {'time':[142,143],'goods':'goods6'},
 {'time':[144,145],'goods':'goods6'},
 {'time':[146,147],'goods':'goods6'},
 {'time':[148,149],'goods':'goods6'},
 {'time':[150,151],'goods':'goods6'},
        ],  #开服第几天，1=开启天数，2=结束天数 （eg：偏移值5点，1号5点开服, 'days':[1, 2]   活动入口会在3号5点关闭）




 'goods1':[
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },






          ],
 'goods2':[
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },





          ],
 'goods3':[
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },





          ],
 'goods4':[
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },


          ],
 'goods5':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },


          ],
 'goods6':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },

          ],
 'goods7':[
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },











          ],
 'goods8':[
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },




          ],
 'goods9':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item176':75},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },

          ],

},










############循环充值#####################
	'rool_pay':{
		'show_moneylimit':300,	#累积充值大于6块的玩家才可见
		'rool_mail_name':'rool_mail_name',
		'rool_mail_info':'rool_mail_info',
		'open_days':{'10_16':'goods1','17_23':'goods2','24_30':'goods3','31_37':'goods4','38_370':'goods5'},  #正式配置 
		'open_date':datetime.datetime(2019,1,16,9,0),  #活动上架时间  开服日期的零点-这个时间的零点 向下取整
		'goods1':{
			'hero':'hero765',#'hero765',
			'show_skill':'skill272',
			'pay_money':1000,			#循环充值金额			
			'first_reward':{
				'item036':20,
				'item765':50,		
			},
			'reward':{
				'item036':20,
				'item765':50,	
			}
		},
		'goods2':{
			'hero':'hero768',
			'show_skill':'skill241',
			'pay_money':1000,			#循环充值金额		
			'first_reward':{
				'item036':20,
				'item768':50,			
			},			
			'reward':{
				'item036':20,
				'item096':1,	
			}
		},
		'goods3':{
			'hero':'hero767',
			'show_skill':'skill273',
			'pay_money':1000,			#循环充值金额	
			'first_reward':{
				'item036':20,
				'item767':50,			
			},			
			'reward':{
				'item036':20,
				'item097':1,	
			}
		},
		'goods4':{
			'hero':'hero766',
			'show_skill':'skill242',
			'pay_money':1000,			#循环充值金额	
			'first_reward':{
				'item036':20,
				'item766':50,			
			},			
			'reward':{
				'item036':20,
				'item098':1,	
			}
		},
		'goods5':{
			'hero':'hero999',
			#'show_skill':'skill273',
			'pay_money':1000,			#循环充值金额	
			'first_reward':{
				'item036':20,
				'item098':1,			
			},			
			'reward':{
				'item036':20,
				'item098':1,	
			}
		}
	},

#################################觉醒拍卖#################################
	'auction':{
		'open_date':datetime.datetime(2019,3,7,14,50),  #活动上架时间(该日期的偏移值时间)  开服时间-配置时间
		'notice':[39600,600,'550007'],		#显示入口时间(秒)，活动开始前X秒进行一次推送（推送后显示入口，但无法点开）点击后显示这个文本,文本中的{0}代表时间，显示单位为分+秒
		'begin_time':[17,0],		#开始时间
		'end_time':[19,0],		#结束时间
		'add_time':[300,300],		#延长时间,距离结束时间(end_time)X秒开始，每次有人加价，则结束时间 = 加价时间 + Y秒
		'latest_time':[19,30],		#落锤时间
		'buy_time':10800,		#购买环节的持续时间，单位/秒
		'unit':100,		#竞价单位下限，黄金
		'warning':[10000,'550017'],	#大额出价提示（超过当前价格10000黄金时提示）
		'anew':'550011',		#出价低于现价（出价时，别人出了更高的价格）
		'bingo':'550032',		#竞标成功
		'info':'550008',		#提示文本（界面最底部）
		'repeat':'550015',		#重复购买提示
		'tips':'550016',		#tips文本
		'show':'550045',		#竞标成功的全服推送，{0}带入玩家名称，{1}带入礼包名称
		'msg_reward':['550009','550010'],		#发奖邮件标题和内容
		'msg_surpass':['550012','550013'],		#退款邮件标题和内容，{0}代表当时竞标消耗的黄金，当竞价被超越时，用第0位的文本显示推送信息
		'end_text':'550014',		#活动已结束的提示
		'list_section':[18,26],		#显示列表的区间
		'library':{		#拍卖库
                2:[
                      [2000,-1,{'item757':100,'awaken':['hero757']}],         #典韦
                
                      [2000,-1,{'item755':100,'awaken':['hero755']}],         #周泰
                          ],
                4:[
                      [2500,-1,{'item729':150,'awaken':['hero729']}],         #姜维
                
                      [2500,-1,{'item702':150,'awaken':['hero702']}],         #许褚
                          ],
                6:[
                      [3000,-1,{'item733':200,'awaken':['hero733']}],         #甘宁
                
                      [3000,-1,{'item701':200,'awaken':['hero701']}],         #马超
                          ],
                8:[
                      [3500,-1,{'item726':250,'awaken':['hero726']}],         #夏侯惇
                
                      [3500,-1,{'item728':250,'awaken':['hero728']}],         #太史慈
                          ],
                10:[
                      [4000,-1,{'item736':300,'awaken':['hero736']}],         #黄忠
                
                      [4000,-1,{'item734':300,'awaken':['hero734']}],         #荀彧
                          ],
                12:[
                      [4500,-1,{'item725':350,'awaken':['hero725']}],         #吕蒙
                
                      [4500,-1,{'item731':350,'awaken':['hero731']}],         #张飞
                          ],
                14:[
                      [5000,-1,{'item704':400,'awaken':['hero704']}],         #马云禄
                
                      [5000,-1,{'item715':400,'awaken':['hero715']}],         #贾诩
                          ],
                16:[
                      [5500,-1,{'item705':450,'awaken':['hero705']}],         #黄月英
                
                      [5500,-1,{'item719':450,'awaken':['hero719']}],         #甄姬
                          ],
                18:[
                      [6000,50000,{'item722':500,'awaken':['hero722']}],         #孙尚香
                
                      [6000,50000,{'item712':500,'awaken':['hero712']}],         #张辽
                          ],
                20:[
                      [6000,50000,{'item703':500,'awaken':['hero703']}],         #貂蝉
                
                      [6000,50000,{'item706':500,'awaken':['hero706']}],         #关羽
                          ],
                22:[
                      [6000,50000,{'item717':500,'awaken':['hero717']}],         #郭嘉
                
                      [6000,50000,{'item710':500,'awaken':['hero710']}],         #孙策
                          ],
                24:[
                      [6000,50000,{'item716':500,'awaken':['hero716']}],         #赵云
                
                      [6000,50000,{'item708':500,'awaken':['hero708']}],         #吕布
                          ],
                26:[
                      [7000,80000,{'item761':500,'awaken':['hero761']}],         #庞统
                
                      [7000,80000,{'item709':500,'awaken':['hero709']}],         #周瑜
                          ],
		}
	},

############微信小程序分享#####################
	'share_reward':[[0,{'coin':50}],[30,{'coin':50}],[60,{'coin':50}]], #[[cd,reward],],

############惊喜礼包#####################   
        'surprise_gift':{
                         'active_time':[[10,00],[22,00]],
                         'openday':{'12_13':'goods1','15_16':'goods1','18_19':'goods1','25_26':'goods2','33_34':'goods3',},

                         'goods1':{
                                    'gd1000':{ # 需要充值的人民币
                                       'name':'surprise_08', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':9300,
                                       'reward':{'item013':25,'item055':10,'item057':30,},
                                      },
                                    'gd1001':{ # 需要充值的人民币
                                       'name':'surprise_09', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':18600,
                                       'reward':{'item013':50,'item055':20,'item057':60,},
                                      },
                                    'gd1002':{ # 需要充值的人民币
                                       'name':'surprise_10', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':35400,
                                       'reward':{'item013':75,'item014':15,'item056':20,'item058':60,},
                                      },
                                    'gd1003':{ # 需要充值的人民币
                                       'name':'surprise_11', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':69350,
                                       'reward':{'item013':150,'item014':40,'item056':30,'item058':90,},
                                      },
                                    'gd1004':{ # 需要充值的人民币
                                       'name':'surprise_15', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':81485,
                                       'reward':{'item014':70,'item015':15,'item056':50,'item058':99,},
                                      },
                         },
                         'goods2':{
                                    'gd1005':{ # 需要充值的人民币
                                       'name':'surprise_08', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':11300,
                                       'reward':{'item013':35,'item055':10,'item057':30,},
                                      },
                                    'gd1006':{ # 需要充值的人民币
                                       'name':'surprise_09', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':23600,
                                       'reward':{'item013':75,'item055':20,'item057':60,},
                                      },
                                    'gd1007':{ # 需要充值的人民币
                                       'name':'surprise_10', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':47700,
                                       'reward':{'item013':99,'item014':30,'item056':20,'item058':60,},
                                      },
                                    'gd1008':{ # 需要充值的人民币
                                       'name':'surprise_11', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':67350,
                                       'reward':{'item013':65,'item014':50,'item015':10,'item056':30,'item058':90,},
                                      },
                                    'gd1009':{ # 需要充值的人民币
                                       'name':'surprise_15', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':108985,
                                       'reward':{'item014':50,'item015':30,'item016':5,'item056':50,'item058':99,},
                                      },
                         },
                         'goods3':{
                                    'gd1010':{ # 需要充值的人民币
                                       'name':'surprise_08', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':14300,
                                       'reward':{'item013':50,'item055':10,'item057':30,},
                                      },
                                    'gd1011':{ # 需要充值的人民币
                                       'name':'surprise_09', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':28400,
                                       'reward':{'item013':99,'item055':20,'item057':60,},
                                      },
                                    'gd1012':{ # 需要充值的人民币
                                       'name':'surprise_10', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':57700,
                                       'reward':{'item013':99,'item014':50,'item056':20,'item058':60,},
                                      },
                                    'gd1013':{ # 需要充值的人民币
                                       'name':'surprise_11', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':98850,
                                       'reward':{'item014':99,'item015':30,'item056':30,'item058':90,},
                                      },
                                    'gd1014':{ # 需要充值的人民币
                                       'name':'surprise_15', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':156485,
                                       'reward':{'item015':35,'item016':20,'item056':50,'item058':99,},
                                      },
                                 },	
                            },	

	
	

   
   




############兑换皮肤币#####################
        'pay_skincoin':{
                         'active_time':[[10,00],[23,00]],
                         'hero_show':'skin708_1',
                         '27_9999':{
                   #                 'pay301':{ # 需要充值的档位ID
                   #                    'name':'skincoin_01', # 礼包名字
                   #                    'icon':'icon_skinsd1.png', # 可缺省，缺省则罗列reward图标
                   #                    'num':-1, # 可购买次数，-1为不限制
                   #                    'exclusion':['mi'],
                   #                    'reward':{'item1091':60,},
                   #                   },
                   #                 'pay302':{ # 需要充值的档位ID
                   #                    'name':'skincoin_02', # 礼包名字
                   #                    'icon':'icon_skinsd2.png', # 可缺省，缺省则罗列reward图标
                   #                    'num':5, # 可购买次数
                   #                    'exclusion':['mi'],
                   #                    'reward':{'item1030':10,'item1007':10,},
                   #                   },
                   #                 'pay303':{ # 需要充值的档位ID
                   #                    'name':'skincoin_03', # 礼包名字
                   #                    'icon':'icon_skinsd3.png', # 可缺省，缺省则罗列reward图标
                   #                    'num':5, # 可购买次数
                   #                    'exclusion':['mi'],
                   #                    'reward':{'item1031':10,'item1032':1,'item1007':15,},
                   #                   },
                                    'gd0004':{ # 需要充值的档位ID
                                       'name':'skincoin_04', # 礼包名字
                                       'icon':'icon_skinsd4.png', # 可缺省，缺省则罗列reward图标
                                       'num':-1, # 可购买次数
                                       'exclusion':[],
                                       'reward':{'item1091':1280,},
                                      },
                                    'gd0005':{ # 需要充值的档位ID
                                       'name':'skincoin_05', # 礼包名字
                                       'icon':'icon_skinsd5.png', # 可缺省，缺省则罗列reward图标
                                       'num':-1, # 可购买次数
                                       'exclusion':[],
                                       'reward':{'item1091':3500,},
                                      },
                                    'gd0006':{ # 需要充值的档位ID
                                       'name':'skincoin_06', # 礼包名字
                                       'icon':'icon_skinsd6.png', # 可缺省，缺省则罗列reward图标
                                       'num':-1, # 可购买次数
                                       'exclusion':[],
                                       'reward':{'item1091':7000,},
                                      },
                                      
                         },
             },

################################

########################永久累计充值#################################
	'permanent_total_pay':{
		'title':'502070',		# 永久累计充值标题
		'icon':'icon_permanent_pay',		# 图标
		'tips':'502071',		# 提示文本
		'always_show':True,		# 永久显示，不受活动时间限制
		'use_total_pay':True,		# 使用玩家总充值金额
		'reward':{		# 奖励配置：充值金额(RMB)：奖励
			500:{'item021':5,'gold':50000,'item030':10},		# 500元奖励
			1000:{'item021':10,'gold':100000,'item030':20,'item036':5},		# 1000元奖励
			2000:{'item021':20,'gold':200000,'item030':40,'item036':10,'item037':50},		# 2000元奖励
			3000:{'item021':30,'gold':300000,'item030':60,'item036':15,'item037':100,'item070':5},		# 3000元奖励
			5000:{'item021':50,'gold':500000,'item030':100,'item036':25,'item037':200,'item070':10,'item071':10},		# 5000元奖励
			8000:{'item021':80,'gold':800000,'item030':160,'item036':40,'item037':300,'item070':15,'item071':15,'item073':5},		# 8000元奖励
			10000:{'item021':100,'gold':1000000,'item030':200,'item036':50,'item037':500,'item070':20,'item071':20,'item073':10,'item074':5},		# 10000元奖励
		},
	},

################################

}