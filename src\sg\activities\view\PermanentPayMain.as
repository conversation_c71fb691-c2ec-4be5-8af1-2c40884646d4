package sg.activities.view
{
    import laya.events.Event;
    import laya.ui.Box;
    import laya.ui.Button;
    import laya.ui.Image;
    import laya.ui.Label;
    import laya.ui.List;
    import laya.ui.VBox;
    import laya.ui.HBox;
    import laya.display.Sprite;

    import sg.activities.model.ModelActivities;
    import sg.activities.model.ModelPermanentPay;
    import sg.boundFor.GotoManager;
    import sg.manager.ViewManager;
    import sg.net.NetMethodCfg;
    import sg.utils.ObjectUtil;
    import sg.utils.Tools;
    import sg.manager.AssetsManager;
    import sg.model.ModelUser;
    import sg.cfg.ConfigApp;

    /**
     * 永久累计充值主界面
     */
    public class PermanentPayMain extends Box
    {
        private var model:ModelPermanentPay = ModelPermanentPay.instance;
        private var rewardList:List;
        private var totalPayLabel:Label;
        private var titleLabel:Label;
        private var tipsLabel:Label;
        private var payButton:Button;
        private var bgImage:Image;

        public function PermanentPayMain()
        {
            this.width = 750;
            this.height = 500;
            this.createUI();
            this.model.on(ModelActivities.UPDATE_DATA, this, this.refreshPanel);
            this.on(Event.DISPLAY, this, this._onDisplay);
        }

        private function createUI():void
        {
            // 背景
            this.bgImage = new Image();
            this.bgImage.skin = "activities/bg_activity.png";
            this.bgImage.width = this.width;
            this.bgImage.height = this.height;
            this.addChild(this.bgImage);

            // 主容器
            var mainBox:VBox = new VBox();
            mainBox.width = this.width;
            mainBox.height = this.height;
            mainBox.space = 10;
            mainBox.align = "center";
            this.addChild(mainBox);

            // 标题区域
            var titleBox:HBox = new HBox();
            titleBox.width = this.width;
            titleBox.height = 60;
            titleBox.align = "center";
            titleBox.space = 10;
            mainBox.addChild(titleBox);

            this.titleLabel = new Label();
            this.titleLabel.text = Tools.getMsgById('502070'); // 永久累计充值
            this.titleLabel.fontSize = 24;
            this.titleLabel.color = "#FFD700";
            this.titleLabel.bold = true;
            titleBox.addChild(this.titleLabel);

            // 提示文本
            this.tipsLabel = new Label();
            this.tipsLabel.text = Tools.getMsgById('502071');
            this.tipsLabel.fontSize = 16;
            this.tipsLabel.color = "#FFFFFF";
            this.tipsLabel.width = this.width - 40;
            this.tipsLabel.wordWrap = true;
            this.tipsLabel.align = "center";
            mainBox.addChild(this.tipsLabel);

            // 当前累计充值显示
            var payInfoBox:HBox = new HBox();
            payInfoBox.width = this.width;
            payInfoBox.height = 40;
            payInfoBox.align = "center";
            payInfoBox.space = 10;
            mainBox.addChild(payInfoBox);

            var payInfoLabel:Label = new Label();
            payInfoLabel.text = "当前累计充值：";
            payInfoLabel.fontSize = 18;
            payInfoLabel.color = "#FFFF00";
            payInfoBox.addChild(payInfoLabel);

            this.totalPayLabel = new Label();
            this.totalPayLabel.fontSize = 18;
            this.totalPayLabel.color = "#00FF00";
            this.totalPayLabel.bold = true;
            payInfoBox.addChild(this.totalPayLabel);

            var yuanLabel:Label = new Label();
            yuanLabel.text = "元";
            yuanLabel.fontSize = 18;
            yuanLabel.color = "#FFFF00";
            payInfoBox.addChild(yuanLabel);

            // 奖励列表
            this.rewardList = new List();
            this.rewardList.width = this.width - 40;
            this.rewardList.height = 300;
            this.rewardList.itemRender = PermanentPayRewardItem;
            this.rewardList.vScrollBarSkin = "";
            this.rewardList.selectEnable = false;
            mainBox.addChild(this.rewardList);

            // 充值按钮
            this.payButton = new Button();
            this.payButton.label = Tools.getMsgById('_public104'); // 前往充值
            this.payButton.width = 150;
            this.payButton.height = 40;
            this.payButton.skin = "common/btn_yellow.png";
            this.payButton.labelColors = "#FFFFFF,#CCCCCC,#FFFFFF";
            this.payButton.on(Event.CLICK, this, this.onPayClick);
            mainBox.addChild(this.payButton);
        }

        private function _onDisplay():void
        {
            this.refreshPanel();
            // 请求数据
            ModelActivities.instance.sendMethod(NetMethodCfg.WS_SR_GET_PERMANENT_PAY_DATA, {});
        }

        private function refreshPanel():void
        {
            if (!this.model.active) return;

            // 更新累计充值金额
            var totalPayMoney:int = this.model.getTotalPayMoney();
            this.totalPayLabel.text = ModelUser.getTenFoldCoin(totalPayMoney).toString();

            // 更新奖励列表
            var rewardListData:Array = this.model.getRewardList();
            this.rewardList.array = rewardListData;
        }

        private function onPayClick():void
        {
            GotoManager.boundForPanel(GotoManager.VIEW_PAY_TEST);
        }

        override public function destroy():void
        {
            this.model.off(ModelActivities.UPDATE_DATA, this, this.refreshPanel);
            super.destroy();
        }
    }
}

/**
 * 永久累计充值奖励项
 */
import laya.ui.Box;
import laya.ui.Button;
import laya.ui.Label;
import laya.ui.HBox;
import laya.ui.VBox;
import laya.events.Event;
import sg.activities.model.ModelPermanentPay;
import sg.activities.view.RewardItem;
import sg.utils.ObjectUtil;
import sg.utils.Tools;
import sg.model.ModelUser;

class PermanentPayRewardItem extends Box
{
    private var model:ModelPermanentPay = ModelPermanentPay.instance;
    private var rewardMoneyLabel:Label;
    private var rewardBox:HBox;
    private var getButton:Button;
    private var statusLabel:Label;
    private var itemData:Object;

    public function PermanentPayRewardItem()
    {
        this.width = 710;
        this.height = 80;
        this.createUI();
    }

    private function createUI():void
    {
        // 主容器
        var mainBox:HBox = new HBox();
        mainBox.width = this.width;
        mainBox.height = this.height;
        mainBox.space = 10;
        mainBox.align = "middle";
        this.addChild(mainBox);

        // 充值金额显示
        var moneyBox:VBox = new VBox();
        moneyBox.width = 120;
        moneyBox.height = this.height;
        moneyBox.align = "center";
        moneyBox.space = 5;
        mainBox.addChild(moneyBox);

        var moneyTitleLabel:Label = new Label();
        moneyTitleLabel.text = "充值";
        moneyTitleLabel.fontSize = 14;
        moneyTitleLabel.color = "#FFFF00";
        moneyBox.addChild(moneyTitleLabel);

        this.rewardMoneyLabel = new Label();
        this.rewardMoneyLabel.fontSize = 16;
        this.rewardMoneyLabel.color = "#00FF00";
        this.rewardMoneyLabel.bold = true;
        moneyBox.addChild(this.rewardMoneyLabel);

        var yuanLabel:Label = new Label();
        yuanLabel.text = "元";
        yuanLabel.fontSize = 14;
        yuanLabel.color = "#FFFF00";
        moneyBox.addChild(yuanLabel);

        // 奖励物品显示
        this.rewardBox = new HBox();
        this.rewardBox.width = 400;
        this.rewardBox.height = this.height;
        this.rewardBox.space = 5;
        this.rewardBox.align = "middle";
        mainBox.addChild(this.rewardBox);

        // 按钮/状态显示
        var buttonBox:VBox = new VBox();
        buttonBox.width = 120;
        buttonBox.height = this.height;
        buttonBox.align = "center";
        buttonBox.space = 5;
        mainBox.addChild(buttonBox);

        this.getButton = new Button();
        this.getButton.label = "领取";
        this.getButton.width = 80;
        this.getButton.height = 30;
        this.getButton.skin = "common/btn_green.png";
        this.getButton.labelColors = "#FFFFFF,#CCCCCC,#FFFFFF";
        this.getButton.on(Event.CLICK, this, this.onGetReward);
        buttonBox.addChild(this.getButton);

        this.statusLabel = new Label();
        this.statusLabel.fontSize = 14;
        this.statusLabel.align = "center";
        buttonBox.addChild(this.statusLabel);
    }

    public function setData(data:Object):void
    {
        this.itemData = data;
        if (!data) return;

        // 设置充值金额
        this.rewardMoneyLabel.text = ModelUser.getTenFoldCoin(data.rewardMoney).toString();

        // 清空奖励显示
        this.rewardBox.removeChildren();

        // 显示奖励物品
        var rewardKeys:Array = ObjectUtil.keys(data.reward);
        for (var i:int = 0; i < rewardKeys.length && i < 6; i++) {
            var itemId:String = rewardKeys[i];
            var itemNum:int = data.reward[itemId];
            
            var rewardItem:RewardItem = new RewardItem();
            rewardItem.width = 60;
            rewardItem.height = 60;
            rewardItem.setData(itemId, itemNum);
            this.rewardBox.addChild(rewardItem);
        }

        // 设置按钮状态
        this.updateButtonStatus(data.status);
    }

    private function updateButtonStatus(status:int):void
    {
        switch (status) {
            case 0: // 不可领取
                this.getButton.visible = false;
                this.statusLabel.visible = true;
                this.statusLabel.text = "未达成";
                this.statusLabel.color = "#999999";
                break;
            case 1: // 可领取
                this.getButton.visible = true;
                this.statusLabel.visible = false;
                this.getButton.disabled = false;
                break;
            case 2: // 已领取
                this.getButton.visible = false;
                this.statusLabel.visible = true;
                this.statusLabel.text = "已领取";
                this.statusLabel.color = "#00FF00";
                break;
        }
    }

    private function onGetReward():void
    {
        if (!this.itemData || this.itemData.status != 1) return;
        this.model.getReward(this.itemData.rewardMoney);
    }
}
