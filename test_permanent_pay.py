#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
永久累计充值功能测试脚本
"""

import sys
import os

# 添加服务器路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'server/service'))

def test_permanent_pay_config():
    """测试永久累计充值配置"""
    print("=== 测试永久累计充值配置 ===")
    
    try:
        # 模拟导入配置
        config_path = "server/trunk/llol/download/configs_202506170127/ploy(活动配置).txt"
        if os.path.exists(config_path):
            print("OK: 活动配置文件存在")

            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'permanent_total_pay' in content:
                    print("OK: 永久累计充值配置已添加")
                else:
                    print("ERROR: 永久累计充值配置未找到")
        else:
            print("ERROR: 活动配置文件不存在")
            
        # 检查文字配置
        msg_path = "server/trunk/llol/download/configs_202506170127/return_msg(文字配置).txt"
        if os.path.exists(msg_path):
            print("OK: 文字配置文件存在")

            with open(msg_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if '502070' in content and '502071' in content:
                    print("OK: 永久累计充值文字配置已添加")
                else:
                    print("ERROR: 永久累计充值文字配置未找到")
        else:
            print("ERROR: 文字配置文件不存在")
            
        # 检查系统配置
        sys_path = "server/trunk/llol/download/configs_202506170127/pf_system_simple(平台杂项).txt"
        if os.path.exists(sys_path):
            print("OK: 系统配置文件存在")

            with open(sys_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'permanent_total_pay' in content:
                    print("OK: 永久累计充值活动入口已添加")
                else:
                    print("ERROR: 永久累计充值活动入口未找到")
        else:
            print("ERROR: 系统配置文件不存在")

    except Exception as e:
        print("ERROR: 配置测试失败: " + str(e))

def test_client_files():
    """测试客户端文件"""
    print("\n=== 测试客户端文件 ===")
    
    files_to_check = [
        "src/sg/activities/model/ModelPermanentPay.as",
        "src/sg/activities/view/PermanentPayMain.as",
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print("OK: " + file_path + " 存在")
        else:
            print("ERROR: " + file_path + " 不存在")

def test_server_files():
    """测试服务器文件"""
    print("\n=== 测试服务器文件修改 ===")
    
    server_file = "server/service/server.py"
    if os.path.exists(server_file):
        print("OK: " + server_file + " 存在")

        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()

        checks = [
            ('permanent_pay_rewards', '永久累计充值奖励字段'),
            ('check_permanent_pay', '永久累计充值检查方法'),
            ('get_permanent_pay_reward', '永久累计充值奖励领取方法'),
            ('get_permanent_pay_data', '永久累计充值数据获取方法'),
        ]

        for check_str, desc in checks:
            if check_str in content:
                print("OK: " + desc + " 已添加")
            else:
                print("ERROR: " + desc + " 未找到")
    else:
        print("ERROR: " + server_file + " 不存在")

def main():
    """主测试函数"""
    print("永久累计充值功能测试")
    print("=" * 50)
    
    test_permanent_pay_config()
    test_client_files()
    test_server_files()
    
    print("\n=== 测试完成 ===")
    print("如果所有项目都显示 OK，说明永久累计充值功能已正确实现")
    print("如果有 ERROR 项目，请检查对应的文件和配置")

if __name__ == "__main__":
    main()
