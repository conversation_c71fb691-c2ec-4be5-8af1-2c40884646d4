#-*- coding: utf-8 -*-
import sys, os.path
import json

DEBUG = True
TEMPLATE_DEBUG = DEBUG
WHERE = 'local'
USER_TABLE_NUM = 0
TIME_ZONE = 'Asia/Shanghai'
## 后台登录是否需要短信验证
LOGIN_CODE_AUTH = False
if WHERE == 'local':
    import socket
    LOGIN_CODE_AUTH = False
    #socket.setdefaulttimeout(5)
    USER_TABLE_NUM = 47
    SUBTEST = False

    USE_CACHE = True
    BASEROOT = '/data/server/analytics_sg3/trunk'
    BASE_URL = 'http://118.178.139.38:7701'

    MEDIA_URL = BASE_URL + "/static"
    CACHE_BACKEND = 'file:///data/server/analytics_sg3/cache_file'
    DB_NAME = 'analytics_sg'
    PFS = ['local']


    DB_ACCOUNT = ('127.0.0.1',27017)
    MYSQL = ('root', '123456', '127.0.0.1', '3306', 'SgLocal')

    DB_ACCOUNT_PASSWORD = {
        'shards': {
            '1': {
                'master': ('root', '123456', '127.0.0.1', '3306', 'SgLocal'),
                'slaver': ('root', '123456', '127.0.0.1', '3306', 'SgLocal'),
                'cache_backend': 'memcached://127.0.0.1:11211',
                },
            },
    }

# elif WHERE == 'sg3':
    # USER_TABLE_NUM = 47
    # BASE_URL = 'http://anasg3.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []

    # DB_ACCOUNT = ('10.10.0.101',27087)
    # MYSQL = ('root', '0okmnji9!!', '10.10.0.4', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'Sg3'),
                # 'slaver': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
                # },
            # },
    # }

# elif WHERE == 'war':
    # BASE_URL = 'http://anawar.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_war'
    # PFS = []

    # DB_ACCOUNT = ('127.0.0.1',27087)
    # MYSQL = ('root', '0okmnji9!!', '10.10.0.4', '3306', 'War')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'War'),
                # 'slaver': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'War'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'war37':
    # BASE_URL = 'http://anawar37.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # ADMIN_COLOR_BG = '#EEFFFF'
    # DB_NAME = 'analytics_war37'
    # PFS = []

    # DB_ACCOUNT = ('127.0.0.1',27087)
    # MYSQL = ('root', '0okmnji9!!', '83b9796e870545df8cc2732b75644c08in01.internal.cn-south-1.mysql.rds.myhuaweicloud.com', '3306', 'War')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '83b9796e870545df8cc2732b75644c08in01.internal.cn-south-1.mysql.rds.myhuaweicloud.com', '3306', 'War'),
                # 'slaver': ('root', '0okmnji9!!', '83b9796e870545df8cc2732b75644c08in01.internal.cn-south-1.mysql.rds.myhuaweicloud.com', '3306', 'War'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'qh':
    # BASE_URL = 'http://anaqh.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_qh'
    # PFS = []

    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('root', '0okmnji9!!', '10.10.0.4', '3306', 'Qh')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'Qh'),
                # 'slaver': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'Qh'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }
# elif WHERE == 'hk':
    # BASE_URL = 'http://anahk.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'
    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_hk'
    # PFS = ['ios_tw','google','r2g_xm']

    # DB_ACCOUNT = ('127.0.0.1',27087)
    # MYSQL = ('root', '0okmnji9!!', '10.10.0.11', '3306', 'HK')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.10.0.11', '3306', 'HK'),
                # 'slaver': ('root', '0okmnji9!!', '10.10.0.11', '3306', 'HK'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
                # },
            # },
    # }
# elif WHERE == 'kr':
    # BASE_URL = 'http://anakr.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # TIME_ZONE = 'Asia/Seoul'

    # DB_ACCOUNT = ('127.0.0.1',27087)
    # MYSQL = ('root', 'Uaz23Wk#xlyr', 'kol1.c7mdehs0dymh.ap-northeast-2.rds.amazonaws.com', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', 'Uaz23Wk#xlyr', 'kol1.c7mdehs0dymh.ap-northeast-2.rds.amazonaws.com', '3306', 'Sg3'),
                # 'slaver': ('root', 'Uaz23Wk#xlyr', 'kol1.c7mdehs0dymh.ap-northeast-2.rds.amazonaws.com', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
                # },
            # },
    # }
# elif WHERE == 'warzd':
    # BASE_URL = 'http://anawarzd.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_wardb'
    # PFS = []

    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('zqjj', 'WWyeWB9znrfqqs6N', 'rm-gw8k244k3h667i3ts.mysql.germany.rds.aliyuncs.com', '3306', 'War')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('zqjj', 'WWyeWB9znrfqqs6N', 'rm-gw8k244k3h667i3ts.mysql.germany.rds.aliyuncs.com', '3306', 'War'),
                # 'slaver': ('zqjj', 'WWyeWB9znrfqqs6N', 'rm-gw8k244k3h667i3ts.mysql.germany.rds.aliyuncs.com', '3306', 'War'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'ea37':
    # BASE_URL = 'http://anaea37.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # TIME_ZONE = 'Asia/Tokyo'

    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('root', '0okmnji9!!', '10.240.40.7', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.240.40.7', '3306', 'Sg3'),
                # 'slaver': ('root', '0okmnji9!!', '10.240.40.7', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'tw37':
    # BASE_URL = 'http://anatw37.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []

    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('root', '0okmnji9!!', '10.240.41.6', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.240.41.6', '3306', 'Sg3'),
                # 'slaver': ('root', '0okmnji9!!', '10.240.41.6', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'jpn':
    # BASE_URL = 'http://anajpn.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # TIME_ZONE = 'Asia/Tokyo'

    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('admin', 'J3eT%#!R9hslSVtM', 'zqwz.crdagrhnszcd.ap-northeast-1.rds.amazonaws.com', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': (
                # 'admin', 'J3eT%#!R9hslSVtM', 'zqwz.crdagrhnszcd.ap-northeast-1.rds.amazonaws.com', '3306', 'Sg3'),
                # 'slaver': (
                # 'admin', 'J3eT%#!R9hslSVtM', 'zqwz.crdagrhnszcd.ap-northeast-1.rds.amazonaws.com', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'vn':
    # BASE_URL = 'http://anavn.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'
    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # TIME_ZONE = 'Asia/Ho_Chi_Minh'

    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('muu_root', 'Fet27RuQenrSH1iG', 'rm-t4n0d05b96tjgifdz.mysql.singapore.rds.aliyuncs.com', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': (
                # 'muu_root', 'Fet27RuQenrSH1iG', 'rm-t4n0d05b96tjgifdz.mysql.singapore.rds.aliyuncs.com', '3306', 'Sg3'),
                # 'slaver': (
                # 'muu_root', 'Fet27RuQenrSH1iG', 'rm-t4n0d05b96tjgifdz.mysql.singapore.rds.aliyuncs.com', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'mg':
    # BASE_URL = 'http://anamg.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # TIME_ZONE = 'America/Regina'

    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('admin', 'QAZqaz!#123456', 'zuiqiangjingjie-db.cvdosbam0s7m.us-east-2.rds.amazonaws.com', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('admin', 'QAZqaz!#123456', 'zuiqiangjingjie-db.cvdosbam0s7m.us-east-2.rds.amazonaws.com', '3306', 'Sg3'),
                # 'slaver': ('admin', 'QAZqaz!#123456', 'zuiqiangjingjie-db.cvdosbam0s7m.us-east-2.rds.amazonaws.com', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }
# elif WHERE == 'wx37':
    # BASE_URL = 'http://anawx37.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('root', '0okmnji9!!', '10.10.0.4', '3306', 'SgWx37')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'SgWx37'),
                # 'slaver': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'SgWx37'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }
# elif WHERE == 'tengxun':
    # BASE_URL = 'http://anatengxun.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('root', '0okmnji9!!', '10.0.0.11', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.0.0.11', '3306', 'Sg3'),
                # 'slaver': ('root', '0okmnji9!!', '10.0.0.11', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }
# elif WHERE == 'indofun':
    # BASE_URL = 'http://w3k-sgana.indofungames.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # TIME_ZONE = 'Asia/Jakarta'
    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('war3kingdom', 'War3Kingdom2021$!', 'rm-d9j8j1ee4v61soe85.mysql.ap-southeast-5.rds.aliyuncs.com', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('war3kingdom', 'War3Kingdom2021$!', 'rm-d9j8j1ee4v61soe85.mysql.ap-southeast-5.rds.aliyuncs.com', '3306', 'Sg3'),
                # 'slaver': ('war3kingdom', 'War3Kingdom2021$!', 'rm-d9j8j1ee4v61soe85.mysql.ap-southeast-5.rds.aliyuncs.com', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'warjh':
    # BASE_URL = 'https://anawarjh.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # TIME_ZONE = 'Asia/Seoul'

    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('root', '0okmnji9!!', '10.20.1.159', '3306', 'War')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.20.1.159', '3306', 'War'),
                # 'slaver': ('root', '0okmnji9!!', '10.20.1.159', '3306', 'War'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }
# elif WHERE == 'cb37kr':
    # BASE_URL = 'http://anacb37kr.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # TIME_ZONE = 'Asia/Seoul'

    # DB_ACCOUNT = ('127.0.0.1',27087)
    # MYSQL = ('root', 'hWCJ$HDajm9SN&&Z', '10.240.65.7', '3306', 'Cb37Kr')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', 'hWCJ$HDajm9SN&&Z', '10.240.65.7', '3306', 'Cb37Kr'),
                # 'slaver': ('root', 'hWCJ$HDajm9SN&&Z', '10.240.65.7', '3306', 'Cb37Kr'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }
# elif WHERE == 'cb':
    # USER_TABLE_NUM = 47
    # BASE_URL = 'http://anacb.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []

    # DB_ACCOUNT = ('127.0.0.1',27087)
    # MYSQL = ('root', '0okmnji9!!', '10.10.0.4', '3306', 'Cb')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'Cb'),
                # 'slaver': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'Cb'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }
# elif WHERE == 'cbbg':
    # BASE_URL = 'http://anacbbg.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []

    # DB_ACCOUNT = ('127.0.0.1',27087)
    # MYSQL = ('root', '0okmnji9!!', '10.10.0.4', '3306', 'CBBugu')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'CBBugu'),
                # 'slaver': ('root', '0okmnji9!!', '10.10.0.4', '3306', 'CBBugu'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'cbhk':
    # BASE_URL = 'http://anacbhk.ptkill.com'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []

    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('root', '0okmnji9!!', '10.10.0.11', '3306', 'CbHK')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', '0okmnji9!!', '10.10.0.11', '3306', 'CbHK'),
                # 'slaver': ('root', '0okmnji9!!', '10.10.0.11', '3306', 'CbHK'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

# elif WHERE == 'cbvn':
    # BASE_URL = 'http://qimou-game-ana.war2.xyz'
    # BASEROOT = '/data/workspace/analytics_sg3/trunk/'

    # MEDIA_URL = BASE_URL + "/static"
    # CACHE_BACKEND = 'file:///data/workspace/analytics_sg3/cache_file'

    # DB_NAME = 'analytics_sg3'
    # PFS = []
    # TIME_ZONE = 'Asia/Ho_Chi_Minh'
    # DB_ACCOUNT = ('127.0.0.1', 27087)
    # MYSQL = ('root', 'Rc0Yw@%Mb;D4', '10.53.157.251', '3306', 'Sg3')

    # DB_ACCOUNT_PASSWORD = {
        # 'shards': {
            # '1': {
                # 'master': ('root', 'Rc0Yw@%Mb;D4', '10.53.157.251', '3306', 'Sg3'),
                # 'slaver': ('root', 'Rc0Yw@%Mb;D4', '10.53.157.251', '3306', 'Sg3'),
                # 'cache_backend': 'memcached://127.0.0.1:11211',
            # },
        # },
    # }

PWD = 'Kj8#mN2$pQ9@vX7!wE5%rT3&uY6*iO1^'

DB_ECHO = False
USE_CACHE = False

if WHERE == 'local':
    ZONE_NUM = 10
else:
    ZONE_NUM = 50

ZONES = {
    '0': [unicode('全区总览', 'utf-8'),0],
    'h1_1': [unicode('1-8合区', 'utf-8'),0],
}




WP_TYPE_DESC = {
            'gamble': u'占卜',
}



SET_BAK = False
TEMPLATE_LOADERS = (
    'django.template.loaders.filesystem.load_template_source',
    'django.template.loaders.app_directories.load_template_source',
)
TEMPLATE_CONTEXT_PROCESSORS = (
    'django.core.context_processors.request',
    'common.context_processors.settings',
)

TEMPLATE_DIRS = (
    BASEROOT + "/src/templates"
)

SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_COOKIE_AGE = 60*6*24*10
SESSION_COOKIE_NAME = 'sessionid'
CACHE_PRE = 'analytics_'

INSTALLED_APPS = (
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'admin',
)


USEING_SHARD_INDEX = '1'

LANGUAGE_CODE = 'zh-cn'

SITE_ID = 1

USE_I18N = False

ROOT_URLCONF = 'urls'
QH_LOG_FILE = '/data/logs/syslog_360.log'
ADQH_LOG_FILE = '/data/logs/syslog_360_ad.log'

## 加载后台文字、背景颜色配置
ADMIN_STYLE_CONFIG_DEFAULT = {
    "TITLE": "统计",
    "ICON": "sg.png",
    "APP_NAME": "local",
    "APP_NAME_CN": "●本地",
    "ADMIN_COLOR_MENU": "#335588",
    "ADMIN_COLOR_TITLE": "#0066FF",
    "ADMIN_COLOR_BG": "#b3b5b9"
}

if sys.platform == 'win32':
    ADMIN_STYLE_CONFIG_FILE = BASEROOT + r'\src\ana_admin_style.json'
else:
    ADMIN_STYLE_CONFIG_FILE = BASEROOT + '/src/ana_admin_style.json'
if os.path.exists(ADMIN_STYLE_CONFIG_FILE):
    ADMIN_STYLE_CONFIG = json.load(file(ADMIN_STYLE_CONFIG_FILE), encoding='utf-8').get(WHERE, ADMIN_STYLE_CONFIG_DEFAULT)
else:
    ADMIN_STYLE_CONFIG = ADMIN_STYLE_CONFIG_DEFAULT
