package sg.activities.model
{
    import sg.model.ViewModelBase;
    import sg.cfg.ConfigServer;
    import sg.manager.ModelManager;
    import sg.utils.ObjectUtil;
    import sg.utils.Tools;
    import sg.activities.ActivityHelper;
    import sg.net.NetMethodCfg;

    /**
     * 永久累计充值模型
     */
    public class ModelPermanentPay extends ViewModelBase
    {
        // 单例
        private static var sModel:ModelPermanentPay = null;
        
        public static function get instance():ModelPermanentPay {
            return sModel ||= new ModelPermanentPay();
        }
        
        private var cfg:Object;
        private var permanentPayData:Object;
        
        public function ModelPermanentPay() {
            // 获取配置
            this.cfg = ConfigServer.ploy['permanent_total_pay'];
            haveConfig = Boolean(cfg);
        }

        override public function refreshData(data:*):void {
            if (!haveConfig) return;
            this.permanentPayData = data;
            this.event(ModelActivities.UPDATE_DATA);
        }

        override public function get active():Boolean {
            if (!haveConfig) return false;
            // 永久累计充值始终激活（如果配置存在）
            return ModelManager.instance.modelUser.canPay;
        }

        override public function get redPoint():Boolean {
            if (!haveConfig || !permanentPayData) return false;
            
            var totalPayMoney:int = permanentPayData.total_pay_money || 0;
            var rewardStatus:Object = permanentPayData.permanent_pay_rewards || {};
            
            // 检查是否有可领取的奖励
            var rewardKeys:Array = ObjectUtil.keys(cfg.reward);
            for (var i:int = 0; i < rewardKeys.length; i++) {
                var rewardMoney:int = parseInt(rewardKeys[i]);
                if (totalPayMoney >= rewardMoney && rewardStatus[rewardKeys[i]] != 1) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 获取总充值金额
         */
        public function getTotalPayMoney():int {
            return permanentPayData ? (permanentPayData.total_pay_money || 0) : 0;
        }

        /**
         * 获取奖励状态
         */
        public function getRewardStatus():Object {
            return permanentPayData ? (permanentPayData.permanent_pay_rewards || {}) : {};
        }

        /**
         * 检查指定档位是否可领取
         */
        public function canGetReward(rewardMoney:int):Boolean {
            var totalPayMoney:int = this.getTotalPayMoney();
            var rewardStatus:Object = this.getRewardStatus();
            
            return totalPayMoney >= rewardMoney && rewardStatus[rewardMoney.toString()] != 1;
        }

        /**
         * 检查指定档位是否已领取
         */
        public function isRewardReceived(rewardMoney:int):Boolean {
            var rewardStatus:Object = this.getRewardStatus();
            return rewardStatus[rewardMoney.toString()] == 1;
        }

        /**
         * 获取奖励列表数据
         */
        public function getRewardList():Array {
            if (!haveConfig) return [];
            
            var rewardKeys:Array = ObjectUtil.keys(cfg.reward);
            var rewardList:Array = [];
            var totalPayMoney:int = this.getTotalPayMoney();
            var rewardStatus:Object = this.getRewardStatus();
            
            // 按金额排序
            rewardKeys.sort(function(a:String, b:String):Number {
                return parseInt(a) - parseInt(b);
            });
            
            for (var i:int = 0; i < rewardKeys.length; i++) {
                var rewardMoney:int = parseInt(rewardKeys[i]);
                var reward:Object = cfg.reward[rewardMoney];
                var status:int = 0; // 0-不可领取，1-可领取，2-已领取
                
                if (rewardStatus[rewardKeys[i]] == 1) {
                    status = 2; // 已领取
                } else if (totalPayMoney >= rewardMoney) {
                    status = 1; // 可领取
                }
                
                rewardList.push({
                    rewardMoney: rewardMoney,
                    reward: reward,
                    status: status
                });
            }
            
            return rewardList;
        }

        /**
         * 领取奖励
         */
        public function getReward(rewardMoney:int):void {
            if (!this.canGetReward(rewardMoney)) {
                return;
            }
            
            ModelActivities.instance.sendMethod(NetMethodCfg.WS_SR_GET_PERMANENT_PAY_REWARD, {
                reward_money: rewardMoney
            });
        }

        /**
         * 获取配置
         */
        public function get config():Object {
            return cfg;
        }
    }
}
