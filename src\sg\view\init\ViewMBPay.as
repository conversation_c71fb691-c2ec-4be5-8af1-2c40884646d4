package sg.view.init
{
    import ui.init.viewMBPayUI;
    import sg.model.ModelMB;
    import laya.events.Event;
    import sg.utils.Tools;
    import laya.display.Sprite;
    import sg.cfg.ConfigServer;
    import sg.manager.ModelManager;
    import sg.model.ModelUser;
    import sg.utils.ArrayUtil;
    import sg.net.NetHttp;
    import sg.model.ModelGame;
    import laya.utils.Handler;
    import sg.cfg.VoucherConfig;

    public class ViewMBPay extends viewMBPayUI
    {
        public var md:ModelMB;
        private var payArg:Array;
        private var httpStatus:Number = -1;
        private var salePid:Boolean = false;

        public function ViewMBPay()
        {
            this.mbPay.offAll();
            this.normalPay.offAll();
            this.mbPay.on(Event.CLICK, this, this.mbPayClick);
            this.normalPay.on(Event.CLICK, this, this.normalPayClick);
        }

        override public function initData():void
        {
            this.payArg = this.currArg;
            var payCfg:Object = ConfigServer.pay_config[this.payArg[0]];
            this.comTitle.setViewTitle(VoucherConfig.getText("voucher_title")); // 代金券充值
            this.money.text = VoucherConfig.getText("voucher_money", [payCfg[0]]);
            this.info.text = VoucherConfig.getText("voucher_info");
            this.mbPay.label = VoucherConfig.getText("voucher_pay"); // 代金券支付
            this.normalPay.label = Tools.getMsgById("MB_common");
            this.mb_have.text = VoucherConfig.getText("voucher_have", [ModelManager.instance.modelUser.getVoucherAmount()]);

            // 调整代金券显示样式：放大字体，向上移动
            // this.mb_have.fontSize = 22; // 从14增加到20
            // this.mb_have.y = this.mb_have.y - 18; // 向上移动15像素
        }

        private function mbPayClick():void
        {
            ModelGame.toPay(payArg[0], payArg[1], salePid, Handler.create(this, this.mbPayFunc));
            this.closeSelf();
        }

        // 代金券支付方法
        private function mbPayFunc():void
        {
            var pid:String = this.payArg[1] || payArg[0];
            var user:ModelUser = ModelManager.instance.modelUser;
            var ldt:Number = ConfigServer.getServerTimer();
            var orderId:String = pid + "|" + user.zone + "|" + user.mUID_base + "|" + ldt;
            var param:Object = {order_id: orderId,
                    sessionid: ModelManager.instance.modelUser.mSessionid};
            NetHttp.instance.send("user_zone.daijinquan_pay", param, Handler.create(this, this.mbPayCallback));
        }

        private function mbPayCallback(data:Object):void
        {
            this.httpStatus = Number(data["server_status"]);
            if (this.httpStatus == NetHttp.STATUS_SERVER_OK)
            {
                // 参考无BUG版本的简单直接方式更新数据
                // if (data.item022_num !== undefined)
                // {
                    ModelManager.instance.modelUser.property.item022 = Number(data.item022_num);
                // }
                // if (data.coin !== undefined) {
                // trace(data.coin, data.item022_num);
                // ModelManager.instance.modelUser.coin = Number(data.coin);
                // }
                // this.closeSelf();
            }
        }

        private function normalPayClick():void
        {
            ModelGame.toPay(payArg[0], payArg[1], salePid);
            this.closeSelf();
        }

        override public function onRemoved():void
        {

        }

    }
}
